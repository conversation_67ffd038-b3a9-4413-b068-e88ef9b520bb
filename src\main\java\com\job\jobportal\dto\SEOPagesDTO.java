package com.job.jobportal.dto;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;

@Data
public class SEOPagesDTO {

    private Long seoPagesId;

    private String seoPagesTitle;

    private String seoPagesUrl;

    private String metaDescription;

    private String metaKeyWord;

    private String featuredImage;

    private String featuredImageKey;

    private String thumbnailImageKey;

    private String thumbnailImage;

    private Integer categoryId;
    private String categoryName;

    private Integer subcategoryId;
    private String subcategoryName;

    private Integer subSubcategoryId;
    private String subSubcategoryName;

    private String customSlug;

    private JsonNode seoPagesDescription;

    private String seoPagesTags;


}
