package com.job.jobportal.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CandidateProfileResponseDTO {
    private CandidateProfileDTO profile;
    private String updatedToken;

    public static CandidateProfileResponseDTO fromProfile(CandidateProfileDTO profile) {
        return new CandidateProfileResponseDTO(profile, null);
    }

    public static CandidateProfileResponseDTO fromProfileAndToken(CandidateProfileDTO profile, String token) {
        return new CandidateProfileResponseDTO(profile, token);
    }

    public boolean hasTokenUpdate() {
        return updatedToken != null && !updatedToken.isEmpty();
    }
}
