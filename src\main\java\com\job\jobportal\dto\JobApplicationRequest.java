package com.job.jobportal.dto;

import com.job.jobportal.util.CommonUtils;
import lombok.Data;

@Data
public class JobApplicationRequest {
    private String fullName;
    private String email;
    private String mobileNumber;
    private String answer;
    private String resumeKey;
    private String resumeUrl;

    public void setFullName(String fullName) {
        this.fullName = CommonUtils.normalizeText(fullName);
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = CommonUtils.normalizeText(mobileNumber);
    }

    public void setAnswer(String answer) {
        this.answer = CommonUtils.normalizeText(answer);
    }
}