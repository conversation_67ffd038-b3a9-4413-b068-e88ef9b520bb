INSERT INTO component_type (id, name) VALUES
(28, 'subscription_status')$$

-- sortOrder values: 1=trial, 2=standard monthly, 3=standard yearly, 4=premium monthly, 5=premium yearly, 6=enterprise monthly, 7=enterprise yearly
UPDATE subscription_plan SET sort_order = 1 WHERE plan_id = 5$$
UPDATE subscription_plan SET sort_order = 2 WHERE plan_id = 2$$
UPDATE subscription_plan SET sort_order = 3 WHERE plan_id = 20$$
UPDATE subscription_plan SET sort_order = 4 WHERE plan_id = 3$$
UPDATE subscription_plan SET sort_order = 5 WHERE plan_id = 30$$
UPDATE subscription_plan SET sort_order = 6 WHERE plan_id = 4$$
UPDATE subscription_plan SET sort_order = 7 WHERE plan_id = 40$$

-- Add master data for subscription status types
INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(28, 0, 'Not Created'),
(28, 1, 'Active'),
(28, 2, 'Cancelled'),
(28, 3, 'Suspended'),
(28, 4, 'Created Not Paid'),
(28, 5, 'Trial'),
(28, 6, 'Pending'),
(28, 7, 'Payment Failed')$$