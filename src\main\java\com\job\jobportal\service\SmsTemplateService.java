package com.job.jobportal.service;

import com.job.jobportal.dto.MarketingUserDTO;
import com.job.jobportal.dto.SMSTemplateDTO;
import com.job.jobportal.dto.SendMarketingDTO;
import com.job.jobportal.model.SMSTemplate;
import com.job.jobportal.repository.SMSTemaplateRepo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SmsTemplateService {

    private static final Logger logger = LoggerFactory.getLogger(SmsTemplateService.class);


    @Autowired
    SMSTemaplateRepo smsTemaplateRepo;

    public List<SMSTemplate> getAllSMSTemplate() {
        try {
            return smsTemaplateRepo.findAll();

        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }

    public int sendMessage(SendMarketingDTO sendMarketingDTO) {
        try {
            SMSTemplate smsTemplate = smsTemaplateRepo.findById(sendMarketingDTO.getTemplateId()).get();

            for (MarketingUserDTO mobile : sendMarketingDTO.getMarketingUserDTOList()
            ) {
                //   smSservice.sendSMS(mobile.getMobileNo(), smsTemplate.getMessage());
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
            throw e;

        }
        return 1;
    }

    public int sendWhatsapp(SendMarketingDTO sendMarketingDTO) {
        try {
            SMSTemplate smsTemplate = smsTemaplateRepo.findById(sendMarketingDTO.getTemplateId()).get();
            for (MarketingUserDTO mobile : sendMarketingDTO.getMarketingUserDTOList()
            ) {
                //   smSservice.sendWhatsApp(mobile.getMobileNo(), smsTemplate.getMessage());
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
            throw e;
        }
        return 1;
    }

    public SMSTemplate addSMSTemplate(SMSTemplateDTO smsTemplateDTO) {
        try {
            SMSTemplate smsTemplate = new SMSTemplate();
            smsTemplate.setMessage(smsTemplateDTO.getMessage());
            return smsTemaplateRepo.save(smsTemplate);

        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }

    public SMSTemplate updateSMSTemplate(SMSTemplateDTO smsTemplateDTO) {
        try {
            SMSTemplate smsTemplate = new SMSTemplate();
            smsTemplate.setMessage(smsTemplateDTO.getMessage());
            smsTemplate.setSmsTemplateId(smsTemplateDTO.getSmsTemplateId());
            return smsTemaplateRepo.save(smsTemplate);

        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }
    public void deleteSmsTemplate(Long templateId) {
        try {
            smsTemaplateRepo.deleteById(templateId);

        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }


}
