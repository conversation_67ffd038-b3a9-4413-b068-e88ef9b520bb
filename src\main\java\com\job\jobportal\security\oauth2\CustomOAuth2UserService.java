package com.job.jobportal.security.oauth2;

import com.job.jobportal.model.Registereduser;
import com.job.jobportal.model.Subscription;
import com.job.jobportal.repository.SubscriptionRepo;
import com.job.jobportal.response.OAuth2AuthenticationProcessingException;
import com.job.jobportal.security.AuthProvider;
import com.job.jobportal.security.UserPrincipal;
import com.job.jobportal.security.oauth2.user.OAuth2UserInfo;
import com.job.jobportal.security.oauth2.user.OAuth2UserInfoFactory;
import com.job.jobportal.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.oauth2.client.userinfo.DefaultOAuth2UserService;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Optional;

@Service
public class CustomOAuth2UserService extends DefaultOAuth2UserService {


    @Autowired
    UserService userService;

    @Autowired
    SubscriptionRepo subscriptionRepo;


    @Override
    public OAuth2User loadUser(OAuth2UserRequest oAuth2UserRequest) throws OAuth2AuthenticationException {
        OAuth2User oAuth2User = super.loadUser(oAuth2UserRequest);

        try {
            return processOAuth2User(oAuth2UserRequest, oAuth2User);
        } catch (AuthenticationException ex) {
            throw ex;
        } catch (Exception ex) {
            // Throwing an instance of AuthenticationException will trigger the OAuth2AuthenticationFailureHandler
            throw new InternalAuthenticationServiceException(ex.getMessage(), ex.getCause());
        }
    }

    private OAuth2User processOAuth2User(OAuth2UserRequest oAuth2UserRequest, OAuth2User oAuth2User) {
        OAuth2UserInfo oAuth2UserInfo = OAuth2UserInfoFactory.getOAuth2UserInfo(oAuth2UserRequest.getClientRegistration().getRegistrationId(), oAuth2User.getAttributes());
        if (ObjectUtils.isEmpty(oAuth2UserInfo.getEmail())) {
            throw new OAuth2AuthenticationProcessingException("Email not found from OAuth2 provider");
        }

        Optional<Registereduser> userOptional = userService.findByEmail(oAuth2UserInfo.getEmail());
        Registereduser user;
        if (userOptional.isPresent()) {
            user = userOptional.get();
            if (user.getProvider() != null) {
                if (!user.getProvider().equals(AuthProvider.valueOf(oAuth2UserRequest.getClientRegistration().getRegistrationId()))) {
                    throw new OAuth2AuthenticationProcessingException("Looks like you're signed up with " +
                            user.getProvider() + " account. Please use your " + user.getProvider() +
                            " account to login.");
                }
                user = userService.updateExistingUser(user, oAuth2UserInfo);
            }
        } else {
            user = userService.registerNewUser(oAuth2UserRequest, oAuth2UserInfo);
        }
        Optional<Subscription> subscription = subscriptionRepo.findByRegistereduser(user);
        UserPrincipal p = UserPrincipal.create(user, oAuth2User.getAttributes(), subscription.orElse(null));
        return p;
    }


}
