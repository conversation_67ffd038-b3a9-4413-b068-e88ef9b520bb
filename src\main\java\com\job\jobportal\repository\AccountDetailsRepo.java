package com.job.jobportal.repository;

import com.job.jobportal.model.AccountDetails;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

@Repository
@Transactional
public interface AccountDetailsRepo extends JpaRepository<AccountDetails, Long> {


    @Modifying
    @Query("UPDATE AccountDetails u SET u.isActive =:isActive  WHERE u.accountDetailsId =:accountDetailsId")
    int updateAccountActiveState(@Param("isActive") int isActive, @Param("accountDetailsId") Long accountDetailsId);


    @Modifying
    @Query("UPDATE AccountDetails u SET u.isPremiumAccount =:isPremiumAccount  WHERE u.accountDetailsId =:accountDetailsId")
    int updatePremiumState(@Param("isPremiumAccount") int isPremiumAccount, @Param("accountDetailsId") Long accountDetailsId);


    @Modifying
    @Query("UPDATE AccountDetails u SET u.storageUse =:storageUse  WHERE u.accountDetailsId =:accountDetailsId")
    int updateStorageUsage(@Param("storageUse") Long storageUse, @Param("accountDetailsId") Long accountDetailsId);

    @Modifying
    @Query("UPDATE AccountDetails u SET u.isDeleteScheduled =:isDeleteScheduled ,u.deleteScheduleDate=:deleteScheduleDate  WHERE u.accountDetailsId =:accountDetailsId")
    int updateDeleteUsage(@Param("isDeleteScheduled") int isDeleteScheduled, @Param("deleteScheduleDate") Timestamp deleteScheduleDate, @Param("accountDetailsId") Long accountDetailsId);


    List<AccountDetails> findAllByIsDeleteScheduled(int isDeleteScheduled);


    @Query(value = "CALL deleteAccount("
            + " :userId,:accountDetailsId)",
            nativeQuery = true)
    void executeDeleteAccountProcedure(
            @Param("userId") Long userId, @Param("accountDetailsId") Long accountDetailsId);

}
