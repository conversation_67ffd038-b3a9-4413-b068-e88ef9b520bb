package com.job.jobportal.controller;

import com.job.jobportal.dto.ContactUsDTO;
import com.job.jobportal.response.ApiResponse;
import com.job.jobportal.service.ContactUsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;


@RestController
@RequestMapping("/api")
public class ContactUsController {

    @Autowired
    private ContactUsService contactUsService;

    @Autowired
    private MessageSource messageSource;

    private static final Logger logger = LoggerFactory.getLogger(ContactUsController.class);

    @PostMapping("/contact-us")
    public ResponseEntity<ApiResponse<String>> submitContactUsForm(@Valid @RequestBody ContactUsDTO contactUsDTO) {
        try {
            contactUsService.processContactUsForm(contactUsDTO);
            String successMessage = messageSource.getMessage("msg.contact_us_form_success", null, LocaleContextHolder.getLocale());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.OK, true, null, successMessage),
                    HttpStatus.OK
            );
        } catch (Exception e) {
            logger.error("Error processing contact us form", e);
            String errorMessage = messageSource.getMessage("msg.contact_us_form_error", null, LocaleContextHolder.getLocale());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, errorMessage),
                    HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }
}
