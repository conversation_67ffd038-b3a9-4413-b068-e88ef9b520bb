package com.job.jobportal.controller;

import com.job.jobportal.dto.CustomerSupportDTO;
import com.job.jobportal.dto.FeedbackDTO;
import com.job.jobportal.model.CustomerSupport;
import com.job.jobportal.model.Feedback;
import com.job.jobportal.response.ApiResponse;
import com.job.jobportal.response.BadRequestException;
import com.job.jobportal.service.HelpAndFeedbackService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class HelpFeedBackController {

    @Autowired
    HelpAndFeedbackService helpAndFeedbackService;

    private static final Logger logger = LoggerFactory.getLogger(HelpFeedBackController.class);


    @PostMapping("/ticket")
    public ResponseEntity<?> addTicket(@RequestBody CustomerSupportDTO customerSupportDTO) {
        try {
            CustomerSupport customerSupport = helpAndFeedbackService.addCustomerIssue(customerSupportDTO);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, customerSupport,
                    "msg.ticket_raised_success"), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);

        }

    }

    @PostMapping("/feedback")
    public ResponseEntity<?> addFeedback(@RequestBody FeedbackDTO feedbackDTO) {
        try {
            Feedback feedback = helpAndFeedbackService.addFeedback(feedbackDTO);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, feedback,
                    "msg.feedback_submitted_success"), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);

        }

    }

    @GetMapping("/feedback")
    public ResponseEntity<?> getFeedback() {
        try {
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, helpAndFeedbackService.getAllFeedback(),
                    "msg.feedback_get_success"), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);

        }

    }
}
