package com.job.jobportal.service;

import com.job.jobportal.dto.ComponentMasterDataDto;
import com.job.jobportal.dto.MasterDataDto;
import com.job.jobportal.dto.MasterDataResponseDto;
import com.job.jobportal.model.ComponentType;
import com.job.jobportal.model.MasterData;
import com.job.jobportal.repository.MasterDataRepository;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class MasterDataService {

    @Autowired
    private MasterDataRepository masterDataRepository;

    @Autowired
    private ModelMapper modelMapper;

    @Autowired
    private MessageSource messageSource;

    private static final Logger logger = LoggerFactory.getLogger(MasterDataService.class);

    public MasterDataResponseDto getMasterData(List<Integer> componentTypeIds) {
        validateComponentTypeIds(componentTypeIds);

        try {
            List<MasterData> masterDataList = masterDataRepository.findByComponentType_IdIn(componentTypeIds);
            if (masterDataList.isEmpty()) {
                throw new RuntimeException(
                        messageSource.getMessage("msg.master_data_not_found",
                                new Object[]{componentTypeIds}, Locale.getDefault())
                );
            }

            List<ComponentMasterDataDto> componentData = masterDataList.stream()
                    .collect(Collectors.groupingBy(MasterData::getComponentType))
                    .entrySet().stream()
                    .map(this::toComponentMasterDataDto)
                    .collect(Collectors.toList());

            logger.info("Fetched master data for componentTypeIds: {}", componentTypeIds);
            return new MasterDataResponseDto(componentData);
        } catch (Exception e) {
            logger.error("Error fetching master data for componentTypeIds: {} - {}", componentTypeIds, e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    private ComponentMasterDataDto toComponentMasterDataDto(Map.Entry<ComponentType, List<MasterData>> entry) {
        try {
            ComponentMasterDataDto dto = new ComponentMasterDataDto(
                    entry.getKey().getName(),
                    String.valueOf(entry.getKey().getId()),
                    entry.getValue().stream()
                            .map(this::toMasterDataDto)
                            .collect(Collectors.toList())
            );
            logger.debug("Mapped ComponentType {} to ComponentMasterDataDto", entry.getKey().getId());
            return dto;
        } catch (Exception e) {
            logger.error("Error mapping ComponentType {}: {}", entry.getKey().getId(), e.getMessage());
            throw new RuntimeException(
                    messageSource.getMessage("msg.mapping_error", null, Locale.getDefault())
            );
        }
    }

    private MasterDataDto toMasterDataDto(MasterData md) {
        try {
            return modelMapper.map(md, MasterDataDto.class);
        } catch (Exception e) {
            logger.error("Error mapping MasterData ID {}: {}", md.getId(), e.getMessage());
            throw new RuntimeException(
                    messageSource.getMessage("msg.mapping_error", null, Locale.getDefault())
            );
        }
    }

    private void validateComponentTypeIds(List<Integer> componentTypeIds) {
        if (componentTypeIds == null || componentTypeIds.isEmpty()) {
            logger.warn("Invalid componentTypeIds: null or empty");
            throw new IllegalArgumentException(
                    messageSource.getMessage("msg.invalid_component_type_ids", null, Locale.getDefault())
            );
        }
        if (componentTypeIds.stream().anyMatch(id -> id == null || id <= 0)) {
            logger.warn("Invalid componentTypeIds: contains null or negative values - {}", componentTypeIds);
            throw new IllegalArgumentException(
                    messageSource.getMessage("msg.invalid_component_type_ids", null, Locale.getDefault())
            );

        }
    }

    public List<MasterDataDto> getSubcategoriesForCategory(Integer categoryId) {
        try {
            List<MasterData> allSubcategories = masterDataRepository.findByComponentType_Id(17);

            List<MasterData> filteredSubcategories = allSubcategories.stream()
                .filter(data -> {
                    String value = data.getValue();
                    if (value != null && value.contains("|")) {
                        String[] parts = value.split("\\|");
                        if (parts.length > 1) {
                            try {
                                int parentId = Integer.parseInt(parts[1]);
                                return parentId == categoryId;
                            } catch (NumberFormatException e) {
                                logger.error("Error parsing parent category ID: {}", e.getMessage());
                                return false;
                            }
                        }
                    }
                    return false;
                })
                .collect(Collectors.toList());

            return filteredSubcategories.stream()
                .map(data -> {
                    MasterDataDto dto = new MasterDataDto();
                    dto.setId(data.getId());
                    dto.setMasterDataId(data.getMasterDataId());

                    String value = data.getValue();
                    if (value != null && value.contains("|")) {
                        dto.setValue(value.split("\\|")[0]);
                    } else {
                        dto.setValue(value);
                    }

                    return dto;
                })
                .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Error fetching subcategories for category ID {}: {}", categoryId, e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    public List<MasterDataDto> getResourceSubcategories(Integer subcategoryId) {
        try {
            List<MasterData> allResourceSubcategories = masterDataRepository.findByComponentType_Id(20);

            List<MasterData> filteredSubcategories = allResourceSubcategories.stream()
                .filter(data -> {
                    String value = data.getValue();
                    if (value != null && value.contains("|")) {
                        String[] parts = value.split("\\|");
                        if (parts.length > 1) {
                            try {
                                int parentId = Integer.parseInt(parts[1]);
                                return parentId == subcategoryId;
                            } catch (NumberFormatException e) {
                                logger.error("Error parsing parent subcategory ID: {}", e.getMessage());
                                return false;
                            }
                        }
                    }
                    return false;
                })
                .collect(Collectors.toList());

            return filteredSubcategories.stream()
                .map(data -> {
                    MasterDataDto dto = new MasterDataDto();
                    dto.setId(data.getId());
                    dto.setMasterDataId(data.getMasterDataId());

                    String value = data.getValue();
                    if (value != null && value.contains("|")) {
                        dto.setValue(value.split("\\|")[0]);
                    } else {
                        dto.setValue(value);
                    }

                    return dto;
                })
                .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Error fetching resource subcategories for subcategory ID {}: {}", subcategoryId, e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

      //Gets job sub-subcategories for a given subcategory ID
    public List<MasterDataDto> getJobSubSubcategories(Integer subcategoryId) {
        try {
            List<MasterData> allSubSubcategories = masterDataRepository.findByComponentType_Id(23);

            List<MasterData> filteredSubSubcategories = allSubSubcategories.stream()
                .filter(data -> {
                    String value = data.getValue();
                    if (value != null && value.contains("|")) {
                        String[] parts = value.split("\\|");
                        if (parts.length > 1) {
                            try {
                                int parentId = Integer.parseInt(parts[1]);
                                return parentId == subcategoryId;
                            } catch (NumberFormatException e) {
                                logger.error("Error parsing parent subcategory ID: {}", e.getMessage());
                                return false;
                            }
                        }
                    }
                    return false;
                })
                .collect(Collectors.toList());

            return filteredSubSubcategories.stream()
                .map(data -> {
                    MasterDataDto dto = new MasterDataDto();
                    dto.setId(data.getId());
                    dto.setMasterDataId(data.getMasterDataId());

                    String value = data.getValue();
                    if (value != null && value.contains("|")) {
                        dto.setValue(value.split("\\|")[0]);
                    } else {
                        dto.setValue(value);
                    }

                    return dto;
                })
                .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Error fetching job sub-subcategories for subcategory ID {}: {}", subcategoryId, e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    public List<MasterDataDto> getSeoSubSubcategories(Integer subcategoryId) {
        try {
            logger.info("Fetching SEO sub-subcategories for subcategory ID {}", subcategoryId);
            List<MasterData> allSubSubcategories = masterDataRepository.findByComponentType_Id(25);

            List<MasterData> filteredSubSubcategories = allSubSubcategories.stream()
                .filter(data -> {
                    String value = data.getValue();
                    if (value != null && value.contains("|")) {
                        String[] parts = value.split("\\|");
                        if (parts.length > 1) {
                            try {
                                int parentId = Integer.parseInt(parts[1]);
                                return parentId == subcategoryId;
                            } catch (NumberFormatException e) {
                                logger.error("Error parsing parent subcategory ID: {}", e.getMessage());
                                return false;
                            }
                        }
                    }
                    return false;
                })
                .toList();

            logger.info("Found {} SEO sub-subcategories for subcategory ID {}", filteredSubSubcategories.size(), subcategoryId);

            return filteredSubSubcategories.stream()
                .map(data -> {
                    MasterDataDto dto = new MasterDataDto();
                    dto.setId(data.getId());
                    dto.setMasterDataId(data.getMasterDataId());

                    String value = data.getValue();
                    if (value != null && value.contains("|")) {
                        dto.setValue(value.split("\\|")[0]);
                    } else {
                        dto.setValue(value);
                    }

                    return dto;
                })
                .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Error fetching SEO sub-subcategories for subcategory ID {}: {}", subcategoryId, e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    public List<MasterDataDto> getDistricts() {
        try {
            List<MasterData> allDistricts = masterDataRepository.findByComponentType_Id(24);

            return allDistricts.stream()
                .map(data -> {
                    MasterDataDto dto = new MasterDataDto();
                    dto.setId(data.getId());
                    dto.setMasterDataId(data.getMasterDataId());
                    dto.setValue(data.getValue());
                    return dto;
                })
                .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Error fetching districts: {}", e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    public List<MasterDataDto> getMasterDataByComponentTypeId(int componentTypeId) {
        List<MasterData> masterDataList = masterDataRepository.findByComponentType_Id(componentTypeId);

        return masterDataList.stream()
            .map(data -> {
                MasterDataDto dto = new MasterDataDto();
                dto.setId(data.getId());
                dto.setMasterDataId(data.getMasterDataId());
                dto.setValue(data.getValue());
                return dto;
            })
            .collect(Collectors.toList());
    }

    /**
     * Returns a list of job skills for the dropdown
     * @return List of MasterDataDto containing job skills
     */
    public List<MasterDataDto> getJobSkills() {
        try {
            // Create a list of predefined job skills based on the provided list
            List<MasterDataDto> jobSkills = new ArrayList<>();

            // Add the job skills from the provided list
            String[] skillNames = {
                "A&E", "Cardiology", "Medical", "General wards", "IT Consultant",
                "ITU/HDU", "Obs & Gynae", "Surgical", "Mental health", "Software Engineer",
                "Theatres", "Midwifery", "Orthopaedics", "Community", "Developer",
                "Endoscopy", "Paediatrics", "ODP", "General Practitioner", "HR Admin",
                "Chemotherapy", "Radiology", "Urology", "Nurse practitioner", "Testing",
                "Neonatal/PICU", "Palliative", "Dialysis", "ENT", "Others"
            };

            for (int i = 0; i < skillNames.length; i++) {
                MasterDataDto skill = new MasterDataDto();
                skill.setId(i + 1);
                skill.setMasterDataId(i + 1);
                skill.setValue(skillNames[i]);
                jobSkills.add(skill);
            }

            return jobSkills;
        } catch (Exception e) {
            logger.error("Error creating job skills list: {}", e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }
}