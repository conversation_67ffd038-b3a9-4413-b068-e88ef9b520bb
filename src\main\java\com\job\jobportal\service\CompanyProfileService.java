package com.job.jobportal.service;

import com.job.jobportal.dto.CompanyProfileDTO;
import com.job.jobportal.dto.CompanyProfileResponse;
import com.job.jobportal.model.CompanyProfile;
import com.job.jobportal.model.Registereduser;
import com.job.jobportal.repository.CompanyProfileRepo;
import com.job.jobportal.repository.RegisteruserRepository;
import com.job.jobportal.response.BadRequestException;
import com.job.jobportal.security.TokenProvider;
import com.job.jobportal.security.UserPrincipal;
import com.job.jobportal.util.CommonUtils;
import com.job.jobportal.util.EncryptionUtil;
import com.stripe.exception.StripeException;
import com.stripe.model.Customer;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service
public class CompanyProfileService {
    @Autowired
    private CompanyProfileRepo companyProfileRepo;

    @Autowired
    private TokenProvider tokenProvider;

    @Autowired
    private ModelMapper modelMapper;

    @Autowired
    RegisteruserRepository registeruserRepository;

    @Autowired
    private JobService jobService;

    @Autowired
    private EncryptionUtil encryptionUtil;

    @Autowired
    private StripeService stripeService;

    private static final Logger logger = LoggerFactory.getLogger(CompanyProfileService.class);

    public Registereduser getUserId() {
        return registeruserRepository.findById(CommonUtils.getUserPrincipal().getId()).orElseThrow(() ->new RuntimeException("Registered User not found"));
    }

    private void validateCompanyProfileDTO(CompanyProfileDTO dto) {
        if (!dto.isAuthorizedPerson()) {
            throw new BadRequestException("Only authorized persons can register an organization");
        }

        if (dto.getAuthorizedPersonFirstName()==null ||
            dto.getAuthorizedPersonLastName()==null ||
            dto.getAuthorizedPersonDesignation()==null) {
            throw new BadRequestException("Authorized person's details are required");
        }

        if (dto.getCompanyRegistrationNumber() != null) {
            dto.setCompanyRegistrationNumber(CommonUtils.normalizeText(dto.getCompanyRegistrationNumber()));
        }

        if (dto.getCompanyName() != null) {
            dto.setCompanyName(CommonUtils.normalizeText(dto.getCompanyName()));
        }

        if (!StringUtils.hasText(dto.getCompanyRegistrationNumber())) {
            throw new BadRequestException("Company registration number is required");
        }

        if (!StringUtils.hasText(dto.getCompanyName())) {
            throw new BadRequestException("Company name is required");
        }

        if (dto.getCompanyAddressState() != null) {
            dto.setCompanyAddressState(CommonUtils.normalizeText(dto.getCompanyAddressState()));
            if (dto.getCompanyAddressState().isEmpty()) {
                throw new BadRequestException("Company address state cannot be empty if provided");
            }
        }

        normalizeCompanyTextFields(dto);
    }

    private void setCurrentUserEmail(CompanyProfileDTO dto) {
        String email = getUserId().getEmail();
        if (email == null) {
            email = getUserId().getUsername();
        }
        dto.setCurrentUserEmail(email);
    }

    private void setCurrentUserEmail(CompanyProfileResponse response) {
        String email = getUserId().getEmail();
        if (email == null) {
            email = getUserId().getUsername();
        }
        response.setCurrentUserEmail(email);
    }

    public CompanyProfileResponse addCompanyProfile(CompanyProfileDTO companyProfileDTO) {
        validateCompanyProfileDTO(companyProfileDTO);
        Registereduser registereduser = registeruserRepository.findByEmail(CommonUtils.getUserPrincipal().getEmail()).get();
        if(registereduser.getCompanyProfile()!=null){
            throw new RuntimeException("Company Already Created For this Account");
        }
        CompanyProfile companyProfile = modelMapper.map(companyProfileDTO, CompanyProfile.class);
        companyProfile.setCompanyIsArchived(false);
        companyProfile.setCompanyIsActive(true);
        CompanyProfile savedCompanyProfile = companyProfileRepo.save(companyProfile);

        registereduser.setCompanyProfile(companyProfile);
        registereduser.setHasCompanyProfileId(true);

        try {
            if (registereduser.getCustomerStripeId() == null) {
                Customer customer = stripeService.createCompanyCustomer(registereduser, companyProfile.getCompanyName());
                registereduser.setCustomerStripeId(customer.getId());
                logger.info("Created Stripe customer for user: {}, company: {}, customer ID: {}",
                    registereduser.getEmail(), companyProfile.getCompanyName(), customer.getId());
            }
        } catch (StripeException e) {
            logger.error("Failed to create Stripe customer for user: {}, error: {}",
                registereduser.getEmail(), e.getMessage());
        }

        registeruserRepository.save(registereduser);

        UserPrincipal userPrincipal = UserPrincipal.create(registereduser,null);
        String newToken = tokenProvider.createToken(userPrincipal);

        CompanyProfileResponse response = new CompanyProfileResponse(savedCompanyProfile,newToken);
        setCurrentUserEmail(response);

        response.setActiveJobCount(jobService.getActiveJobCountForCompany(savedCompanyProfile.getCompanyProfileId()));

        return response;
    }

    public CompanyProfile updateCompanyProfile(CompanyProfileDTO companyProfileDTO) {
        validateCompanyProfileDTO(companyProfileDTO);

        CompanyProfile existingCompanyProfile = companyProfileRepo.findById(companyProfileDTO.getCompanyProfileId())
                .orElseThrow(() -> new RuntimeException("Company Profile not found"));
        modelMapper.map(companyProfileDTO, existingCompanyProfile);
        CompanyProfile updated = companyProfileRepo.save(existingCompanyProfile);
        setCurrentUserEmail(companyProfileDTO);
        logger.info("Updated company profile with normalized text fields: {}", companyProfileDTO.getCompanyName());
        return updated;
    }

    public CompanyProfile getCompanyProfileById(Long companyProfileId) {
        return companyProfileRepo.findByCompanyProfileIdAndCompanyIsArchivedFalseAndCompanyIsActiveTrue(companyProfileId)
                .orElseThrow(() -> new RuntimeException("Company Profile not found, is archived, or is inactive"));
    }

    public CompanyProfile getCompanyProfile() {
        try {
            Registereduser registereduser = registeruserRepository
                .findCompanyProfileByUserId(CommonUtils.getUserPrincipal().getId())
                .orElseThrow(() -> new RuntimeException("Company Not Linked To Registered User"));

            CompanyProfile companyProfile = registereduser.getCompanyProfile();
            if (companyProfile == null) {
                CompanyProfile emptyProfile = new CompanyProfile();
                String email = getUserId().getEmail();
                if (email == null) {
                    email = getUserId().getUsername();
                }
                emptyProfile.setCurrentUserEmail(email);
                return emptyProfile;
            }

            String email = getUserId().getEmail();
            if (email == null) {
                companyProfile.setCompanyEmail(getUserId().getUsername());
            }
            companyProfile.setCurrentUserEmail(email);

            try {
                long totalPositions = jobService.getActiveJobCountForCompany(companyProfile.getCompanyProfileId());
                companyProfile.setTransientActiveJobCount(totalPositions);

                long activeJobPostCount = jobService.getActiveJobPostCountForCompany(companyProfile.getCompanyProfileId());
                companyProfile.setActiveJobCount(activeJobPostCount);
            } catch (Exception e) {
                logger.error("Error setting active job count: {}", e.getMessage());
            }

            return companyProfile;
        } catch (Exception e) {
            CompanyProfile emptyProfile = new CompanyProfile();
            String email = getUserId().getEmail();
            if (email == null) {
                email = getUserId().getUsername();
            }
            emptyProfile.setCurrentUserEmail(email);
            return emptyProfile;
        }
    }

    public Page<CompanyProfile> getFilteredCompanies(
            Boolean isArchived, Boolean isActive, String companyName, String companyAddressCity,
            String sortBy, String sortDirection, int page, int size) {

        Sort.Direction direction = Sort.Direction.fromString(sortDirection);
        Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));

        Page<CompanyProfile> companies = companyProfileRepo.findAllByFilters(isArchived, isActive, companyName, companyAddressCity, pageable);

        companies.forEach(company -> {
            try {
                long totalPositions = jobService.getActiveJobCountForCompany(company.getCompanyProfileId());
                company.setTransientActiveJobCount(totalPositions);

                long activeJobPostCount = jobService.getActiveJobPostCountForCompany(company.getCompanyProfileId());
                company.setActiveJobCount(activeJobPostCount);
            } catch (Exception e) {
                logger.error("Error setting active job count for company {}: {}", company.getCompanyProfileId(), e.getMessage());
            }
        });

        return companies;
    }

    public CompanyProfile getOneCompanyProfileById(Long companyProfileId) {
        try {
            CompanyProfile companyProfile = companyProfileRepo.findById(companyProfileId)
                    .orElse(new CompanyProfile());

            String email = getUserId().getEmail();
            if (email == null) {
                email = getUserId().getUsername();
            }

            companyProfile.setCurrentUserEmail(email);
            companyProfile.setCompanyEmail(email);
            return companyProfile;
        } catch (Exception e) {
            CompanyProfile emptyProfile = new CompanyProfile();
            String email = getUserId().getEmail();
            if (email == null) {
                email = getUserId().getUsername();
            }
            emptyProfile.setCurrentUserEmail(email);
            emptyProfile.setCompanyEmail(email);
            return emptyProfile;
        }
    }

    public CompanyProfileDTO updateCompanyArchiveStatus(Long companyProfileId, boolean isArchived) {
        CompanyProfile companyProfile = companyProfileRepo.findById(companyProfileId)
                .orElseThrow(() -> new RuntimeException("Company Profile not found"));

        companyProfile.setCompanyIsArchived(isArchived);
        companyProfile.setCompanyIsActive(!isArchived);
        companyProfile = companyProfileRepo.save(companyProfile);
        CompanyProfileDTO dto = modelMapper.map(companyProfile, CompanyProfileDTO.class);
        setCurrentUserEmail(dto);

        try {
            long activeJobCount = jobService.getActiveJobCountForCompany(companyProfile.getCompanyProfileId());
            dto.setActiveJobCount(activeJobCount);
        } catch (Exception e) {
            logger.error("Error setting active job count in DTO: {}", e.getMessage());
        }

        return dto;
    }

    public void deleteCompany(Long companyProfileId) {
        CompanyProfile companyProfile = companyProfileRepo.findById(companyProfileId)
                .orElseThrow(() -> new RuntimeException("Company Profile not found"));
        companyProfileRepo.delete(companyProfile);
    }

    public CompanyProfileDTO updateCompanyActiveStatus(Long companyProfileId, boolean isActive) {
        CompanyProfile companyProfile = companyProfileRepo.findById(companyProfileId)
                .orElseThrow(() -> new RuntimeException("Company Profile not found"));

        if(isActive){
            this.updateCompanyArchiveStatus(companyProfileId, false);
        }else {
            companyProfile.setCompanyIsActive(false);
        }

        companyProfile = companyProfileRepo.save(companyProfile);
        CompanyProfileDTO dto = modelMapper.map(companyProfile, CompanyProfileDTO.class);
        setCurrentUserEmail(dto);

        try {
            long activeJobCount = jobService.getActiveJobCountForCompany(companyProfile.getCompanyProfileId());
            dto.setActiveJobCount(activeJobCount);
        } catch (Exception e) {
            logger.error("Error setting active job count in DTO: {}", e.getMessage());
        }

        return dto;
    }

    public CompanyProfile getPublicCompanyProfileById(Long companyProfileId) {
        CompanyProfile companyProfile = companyProfileRepo.findById(companyProfileId)
            .orElseThrow(() -> new BadRequestException("Company Profile not found"));

        try {
            long totalPositions = jobService.getActiveJobCountForCompany(companyProfileId);
            companyProfile.setTransientActiveJobCount(totalPositions);

            long activeJobPostCount = jobService.getActiveJobPostCountForCompany(companyProfileId);
            companyProfile.setActiveJobCount(activeJobPostCount);
        } catch (Exception e) {
            logger.error("Error setting active job count: {}", e.getMessage());
        }

        return companyProfile;
    }

    public CompanyProfile updateEmailDomainSettings(Long companyProfileId, String emailDomainHost,
                                                    String emailDomainUsername, String emailDomainPassword) {
        CompanyProfile companyProfile = companyProfileRepo.findById(companyProfileId)
                .orElseThrow(() -> new RuntimeException("Company Profile not found"));

        companyProfile.setEmailDomainHost(emailDomainHost);
        companyProfile.setEmailDomainUsername(emailDomainUsername);

        if (emailDomainPassword != null && !emailDomainPassword.isEmpty()) {
            companyProfile.setEmailDomainPassword(encryptionUtil.encrypt(emailDomainPassword));
        }

        return companyProfileRepo.save(companyProfile);
    }

    public String getDecryptedEmailDomainPassword(CompanyProfile companyProfile) {
        if (companyProfile == null || companyProfile.getEmailDomainPassword() == null) {
            return null;
        }

        return encryptionUtil.decrypt(companyProfile.getEmailDomainPassword());
    }

    private void normalizeCompanyTextFields(CompanyProfileDTO dto) {
        if (dto == null) return;

        if (dto.getCompanyName() != null) {
            dto.setCompanyName(CommonUtils.normalizeText(dto.getCompanyName()));
        }

        if (dto.getCompanyEmail() != null) {
            dto.setCompanyEmail(CommonUtils.normalizeText(dto.getCompanyEmail()));
        }

        if (dto.getCompanyWebsite() != null) {
            dto.setCompanyWebsite(CommonUtils.normalizeText(dto.getCompanyWebsite()));
        }

        if (dto.getCompanyPhoneNumber() != null) {
            dto.setCompanyPhoneNumber(CommonUtils.normalizeText(dto.getCompanyPhoneNumber()));
        }

        if (dto.getCompanyAddressCity() != null) {
            dto.setCompanyAddressCity(CommonUtils.normalizeText(dto.getCompanyAddressCity()));
        }

        if (dto.getCompanyAddressState() != null) {
            dto.setCompanyAddressState(CommonUtils.normalizeText(dto.getCompanyAddressState()));
        }

        if (dto.getCompanyAddressCountry() != null) {
            dto.setCompanyAddressCountry(CommonUtils.normalizeText(dto.getCompanyAddressCountry()));
        }

        if (dto.getCompanyAddressDistrict() != null) {
            dto.setCompanyAddressDistrict(CommonUtils.normalizeText(dto.getCompanyAddressDistrict()));
        }

        if (dto.getCompanyAddressLineOne() != null) {
            dto.setCompanyAddressLineOne(CommonUtils.normalizeText(dto.getCompanyAddressLineOne()));
        }

        if (dto.getCompanyAddressLineTwo() != null) {
            dto.setCompanyAddressLineTwo(CommonUtils.normalizeText(dto.getCompanyAddressLineTwo()));
        }

        if (dto.getCompanyAddressPincode() != null) {
            dto.setCompanyAddressPincode(CommonUtils.normalizeText(dto.getCompanyAddressPincode()));
        }

        if (dto.getAuthorizedPersonFirstName() != null) {
            dto.setAuthorizedPersonFirstName(CommonUtils.normalizeText(dto.getAuthorizedPersonFirstName()));
        }

        if (dto.getAuthorizedPersonLastName() != null) {
            dto.setAuthorizedPersonLastName(CommonUtils.normalizeText(dto.getAuthorizedPersonLastName()));
        }

        if (dto.getAuthorizedPersonDesignation() != null) {
            dto.setAuthorizedPersonDesignation(CommonUtils.normalizeText(dto.getAuthorizedPersonDesignation()));
        }

        if (dto.getCompanyRegistrationNumber() != null) {
            dto.setCompanyRegistrationNumber(CommonUtils.normalizeText(dto.getCompanyRegistrationNumber()));
        }

        logger.info("Normalized text fields for company: {}", dto.getCompanyName());
    }
}
