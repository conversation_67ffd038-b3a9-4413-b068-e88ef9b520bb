package com.job.jobportal.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class JobApplicationResponse {
    private CandidateProfileDTO candidateProfileDTO;
    private String jwtToken;
    private String refreshToken;
    private String jobCategoryName;
    private String jobSubCategoryName;
    private String jobSubSubCategoryName;

    public JobApplicationResponse(CandidateProfileDTO candidateProfileDTO, String jwtToken, String refreshToken, String jobCategoryName) {
        this.candidateProfileDTO = candidateProfileDTO;
        this.jwtToken = jwtToken;
        this.refreshToken = refreshToken;
        this.jobCategoryName = jobCategoryName;
    }
}