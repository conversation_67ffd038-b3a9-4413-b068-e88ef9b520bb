package com.job.jobportal.controller;

import com.job.jobportal.dto.BlogDTO;
import com.job.jobportal.model.BlogWebsite;
import com.job.jobportal.response.ApiResponse;
import com.job.jobportal.response.BadRequestException;
import com.job.jobportal.service.BlogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
//import org.springframework.hateoas.EntityModel;
//import org.springframework.hateoas.server.mvc.WebMvcLinkBuilder;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

@RestController
public class BlogController {

    @Autowired
    MessageSource message;

    @Autowired
    BlogService blogService;

    private static final Logger logger = LoggerFactory.getLogger(BlogController.class);

    @PostMapping("/blog/addthumbnail")
    public ResponseEntity<?> addThumbnail(@RequestParam("file") MultipartFile file) {
        try {
            Map<String, Object> url = blogService.addThumbnail(file);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, url, message.getMessage("msg.thumbnail_added_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/blog")
    public ResponseEntity<?> addBlog(@RequestBody BlogDTO blogDTO) {
        try {
            BlogWebsite blogwebsite = blogService.addBlog(blogDTO);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, blogwebsite, message.getMessage("msg.blog_added_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);

        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, message.getMessage("msg.something_went_wrong", null, LocaleContextHolder.getLocale())), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @PutMapping("/blog")
    public ResponseEntity<?> updateBlog(@RequestBody BlogDTO blogDTO) {
        try {
            BlogWebsite blogwebsite = blogService.updateBlog(blogDTO);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, blogwebsite, message.getMessage("msg.blog_updated_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);

        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, message.getMessage("msg.something_went_wrong", null, LocaleContextHolder.getLocale())), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @DeleteMapping("/blog/{blogId}")
    public ResponseEntity<?> deleteBlog(@PathVariable("blogId") Long blogId) {
        try {
            blogService.deleteBlog(blogId);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null, message.getMessage("msg.blog_deleted_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);

        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, message.getMessage("msg.something_went_wrong", null, LocaleContextHolder.getLocale())), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/blog")
    public ResponseEntity<?> getBlog(@RequestParam(defaultValue = "0") int page,
                                     @RequestParam(defaultValue = "5") int size, @RequestParam(required = false) String search) {
        try {
            Map<String, Object> map = blogService.getAllBlog(page, size, search);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, map, message.getMessage("msg.blog_get_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);

        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, message.getMessage("msg.something_went_wrong", null, LocaleContextHolder.getLocale())), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

//    @GetMapping("/blog/{blogId}")
//    public ResponseEntity<?> getBlogDetails(@PathVariable("blogId") Long blogId) {
//        try {
//            BlogWebsite blogwebsite = blogService.getBlog(blogId);
//
//            EntityModel<BlogWebsite> resource= EntityModel.of(blogwebsite);
//
//            resource.add(WebMvcLinkBuilder
//                    .linkTo(WebMvcLinkBuilder
//                    .methodOn(BlogController.class)
//                            .getBlogDetails(blogId))
//                    .withSelfRel());
//
//            Long previousBlogPostId=blogService.getPreviousBlogId(blogId);
//            if(previousBlogPostId!=null){
//                resource.add(WebMvcLinkBuilder
//                        .linkTo(WebMvcLinkBuilder
//                                .methodOn(BlogController.class)
//                                .getBlogDetails(previousBlogPostId))
//                        .withRel("previous"));
//            }
//
//            Long nextBlogId=blogService.getNextBlogId(blogId);
//            if(nextBlogId!=null){
//                resource.add(WebMvcLinkBuilder
//                        .linkTo(WebMvcLinkBuilder
//                                .methodOn(BlogController.class)
//                                .getBlogDetails(nextBlogId))
//                        .withRel("next"));
//            }
//
//            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, resource, message.getMessage("msg.blog_details_get_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
//
//        } catch (BadRequestException e) {
//            logger.error(e.getMessage());
//            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);
//
//        } catch (Exception e) {
//            logger.error(e.getMessage());
//            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, message.getMessage("msg.something_went_wrong", null, LocaleContextHolder.getLocale())), HttpStatus.INTERNAL_SERVER_ERROR);
//        }
//    }

    @GetMapping("/blog/{blogId}")
    public ResponseEntity<?> getBlogDetails(@PathVariable("blogId") Long blogId) {
        try {
            Map<String,Object> blogwebsite = blogService.getBlog(blogId);

            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, blogwebsite, message.getMessage("msg.blog_details_get_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);

        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, message.getMessage("msg.something_went_wrong", null, LocaleContextHolder.getLocale())), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }





}
