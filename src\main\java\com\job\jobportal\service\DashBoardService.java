package com.job.jobportal.service;


import com.job.jobportal.repository.RegisteruserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class DashBoardService {


    @Autowired
    RegisteruserRepository registeruserRepository;


    private static final Logger logger = LoggerFactory.getLogger(DashBoardService.class);


    public Map<String, Object> getAllDetails() {
        try {
            Map<String, Object> map = new HashMap<>();

            //course
            Map<String, Object> courseMap = new HashMap<>();
//            courseMap.put("courseCountActive", courseRepo.countByIsCourseActive(ConstantsUtil.E_COURSE_ACTIVE));
//            courseMap.put("courseCountDraft", courseRepo.countByIsCourseActive(ConstantsUtil.E_COURSE_DRAFT));
//            courseMap.put("purchaseCourse", purchasedCourseRepo.countByPurchaseStatus(ConstantsUtil.COURSE_PURCHASED));
//            courseMap.put("enrolledCourse", purchasedCourseRepo.countByPurchaseStatus(ConstantsUtil.COURSE_ENROLLED));
//            courseMap.put("freeCourse", courseRepo.countByIsFreeCourse(1));

            Map<String, Object> userMap = new HashMap<>();
//            userMap.put("noOfUser", registeruserRepository.findAllByMarketingUser(ConstantsUtil.USER_ROLE).size());
//            userMap.put("subscribedUser", registeruserRepository.countBySubscriptionActive(1));
//            userMap.put("cancelledUser", registeruserRepository.countBySubscriptionType(ConstantsUtil.CANCELLED_PLAN));
//            userMap.put("normalUser", registeruserRepository.countBySubscriptionType(ConstantsUtil.NORMAL_PLAN));
            map.put("user", userMap);
            map.put("course", courseMap);
            return map;
        } catch (Exception e) {
            logger.error(e.getMessage());
            throw e;
        }
    }
}
