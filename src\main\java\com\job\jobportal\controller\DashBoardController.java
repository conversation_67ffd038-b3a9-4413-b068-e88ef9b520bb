package com.job.jobportal.controller;

import com.job.jobportal.response.ApiResponse;
import com.job.jobportal.response.BadRequestException;
import com.job.jobportal.service.DashBoardService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
public class DashBoardController {

    private static final Logger logger = LoggerFactory.getLogger(DashBoardController.class);


    @Autowired
    DashBoardService dashBoardService;

    @GetMapping("/dashboard")
    public ResponseEntity<?> dashboardDetails() {

        try {
            Map<String, Object> map = dashBoardService.getAllDetails();
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, map,
                    "msg.dashboard_details_get_success"), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);

        }

    }
}
