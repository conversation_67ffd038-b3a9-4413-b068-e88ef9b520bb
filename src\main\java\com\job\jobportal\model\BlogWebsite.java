package com.job.jobportal.model;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.Type;
import org.hibernate.type.SqlTypes;


import java.sql.Timestamp;

@Entity
@Data
public class BlogWebsite {

    @Id
    @SequenceGenerator(name = "blog_seq_id")
    @GeneratedValue(generator = "blog_seq_id", strategy = GenerationType.AUTO)
    private Long blogId;

    private String blogTitle;

    private String blogUrl;

    private String metaDescription;

    private String metaKeyWord;

    @Column(columnDefinition = "LONGTEXT")
    private String featuredImage;

    @Column(columnDefinition = "LONGTEXT")
    private String thumbnailImage;

    private String featuredImageKey;

    private String thumbnailImageKey;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "json")
    private JsonNode blogDescription;


    private String blogTags;

    private Timestamp blogCreatedDate;

}
