package com.job.jobportal.service;

import com.job.jobportal.security.UserPrincipal;
import com.job.jobportal.util.CommonUtils;
import com.job.jobportal.util.ConstantsUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class S3Service {

    @Autowired
    private S3Client amazonS3Client;



    @Value("${application.aws.bucketname}")
    private String bucketName;

    private static final Logger logger = LoggerFactory.getLogger(S3Service.class);


    public String addObject(MultipartFile mufile, Long profileId) throws Exception {
        String url = "";
        try {
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(profileId + "/" + mufile.getOriginalFilename())
                    .acl(ObjectCannedACL.PUBLIC_READ)
                    .build();

            PutObjectResponse putObjectResponse = amazonS3Client.putObject(putObjectRequest,
                    software.amazon.awssdk.core.sync.RequestBody.fromInputStream(mufile.getInputStream(), mufile.getSize()));
            url = getUrl(profileId + "/" + mufile.getOriginalFilename());
            UserPrincipal userPrincipal = CommonUtils.getUserPrincipal();
          //  accountDetailsService.updateStorage(mufile.getSize(), ConstantsUtil.ADD_FILE_STORAGE, userPrincipal.getUsername());
        } catch (Exception e) {
            logger.error(e.getMessage());
            throw e;
        }
        return url;
    }

    public String addObject(MultipartFile mufile) throws Exception {
        String url = "";
        try {
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(mufile.getOriginalFilename())
                    .acl(ObjectCannedACL.PUBLIC_READ)
                    .build();

            PutObjectResponse putObjectResponse = amazonS3Client.putObject(putObjectRequest,
                    software.amazon.awssdk.core.sync.RequestBody.fromInputStream(mufile.getInputStream(), mufile.getSize()));
            url = getUrl(mufile.getOriginalFilename());
            UserPrincipal userPrincipal = CommonUtils.getUserPrincipal();
          //  accountDetailsService.updateStorage(mufile.getSize(), ConstantsUtil.ADD_FILE_STORAGE, userPrincipal.getUsername());
        } catch (Exception e) {
            logger.error(e.getMessage());
            throw e;
        }
        return url;
    }

    public void deleteObject(String awsKey) {
        try {
            DeleteObjectRequest deleteObjectRequest = DeleteObjectRequest.builder()
                    .bucket(bucketName)
                    .key(awsKey)
                    .build();
            amazonS3Client.deleteObject(deleteObjectRequest);

            HeadObjectRequest headObjectRequest = HeadObjectRequest.builder()
                    .bucket(bucketName)
                    .key(awsKey)
                    .build();
            HeadObjectResponse headObjectResponse = amazonS3Client.headObject(headObjectRequest);

            long contentLength = headObjectResponse.contentLength();
            UserPrincipal userPrincipal = CommonUtils.getUserPrincipal();
         //   accountDetailsService.updateStorage(contentLength, ConstantsUtil.REMOVE_FILE_STORAGE, userPrincipal.getUsername());
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    private String getUrl(String key) {
        return amazonS3Client.utilities().getUrl(builder -> builder.bucket(bucketName).key(key)).toString();
    }

    public void deleteFilesInFolder(String folderName) {
        ListObjectsV2Request listObjectsRequest = ListObjectsV2Request.builder()
                .bucket(bucketName)
                .prefix(folderName + "/")
                .build();

        ListObjectsV2Response listObjectsResponse = amazonS3Client.listObjectsV2(listObjectsRequest);

        List<ObjectIdentifier> objectsToDelete = listObjectsResponse.contents().stream()
                .map(S3Object::key)
                .map(key -> ObjectIdentifier.builder().key(key).build())
                .collect(Collectors.toList());

        if (!objectsToDelete.isEmpty()) {
            DeleteObjectsRequest deleteObjectsRequest = DeleteObjectsRequest.builder()
                    .bucket(bucketName)
                    .delete(Delete.builder().objects(objectsToDelete).build())
                    .build();

            amazonS3Client.deleteObjects(deleteObjectsRequest);
        }
    }
}
