package com.job.jobportal.model;

import com.job.jobportal.security.AuthProvider;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;
import java.util.Set;

@Entity
@Getter
@Setter
public class Registereduser {

    @Id
    @SequenceGenerator(
            name = "user_seq_id",
            initialValue = 3,
            allocationSize = 1
    )
    @GeneratedValue(generator = "user_seq_id", strategy = GenerationType.SEQUENCE)
    private Long userid;

    private String username;

    @JsonIgnore
    private String password;

    private String imageUrl;

    private String confirmpassword;


    @Enumerated(EnumType.STRING)
    private AuthProvider provider;

    private String providerId;

    private String firstname;

    private String lastname;

    private String mobileno;

    @Column(unique = true)
    private String email;


    private Timestamp createdOn;


    private String refreshToken;

    private String notificationToken;


    @ManyToMany(fetch = FetchType.EAGER, cascade = CascadeType.MERGE)
    @JoinTable(name = "users_roles", joinColumns = {@JoinColumn(name = "user_id")}, inverseJoinColumns = {
            @JoinColumn(name = "role_id")})
    private Set<Roles> roles;


    @Column(columnDefinition = "integer default 0")
    private int hasPassword;


    @OneToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JoinColumn(name = "accountDetailsId")
    private AccountDetails accountDetails;

    private String customerStripeId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "companyProfileId", nullable = true)  // Nullable allows for a user not to belong to a company
    private CompanyProfile companyProfile;

    private boolean hasCompanyProfileId;
    private boolean hasCandidateProfile;



}
