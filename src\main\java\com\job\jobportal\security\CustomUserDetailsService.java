package com.job.jobportal.security;


import com.job.jobportal.model.Registereduser;
import com.job.jobportal.model.Subscription;
import com.job.jobportal.repository.RegisteruserRepository;
import com.job.jobportal.repository.SubscriptionRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Created by r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 02/08/17.
 */

@Service
public class CustomUserDetailsService implements UserDetailsService {

    @Autowired
    RegisteruserRepository userRepository;

    @Autowired
    SubscriptionRepo subscriptionRepo;

    @Override
    @Transactional
    public UserDetails loadUserByUsername(String email)
            throws UsernameNotFoundException {
        Registereduser user = userRepository.findByEmail(email)
                .orElseThrow(() ->
                        new UsernameNotFoundException("User not found with email : " + email)
                );
        Optional<Subscription> subscription = subscriptionRepo.findByRegistereduser(user);

        return UserPrincipal.create(user, subscription.orElse(null));
    }

    @Transactional
    public UserDetails loadUserById(Long id) {
        Optional<Registereduser> user = userRepository.findById(id);
        if (!user.isPresent()) {
            throw new UsernameNotFoundException("User not found with id : " + id);
        }
        Optional<Subscription> subscription = subscriptionRepo.findByRegistereduser(user.get());
        return UserPrincipal.create(user.get(), subscription.orElse(null));
    }
}