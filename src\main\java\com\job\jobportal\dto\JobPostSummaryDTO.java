package com.job.jobportal.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class JobPostSummaryDTO {
    private List<JobSummaryDTO> jobs;

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class JobSummaryDTO {
        private Long jobId;
        private String jobTitle;
        private String companyName;
        private String location;
        private SummaryDTO summary;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SummaryDTO {
        private Long total;
        private Long applied;
        private Long approved;
        private Long rejected;
    }
}
