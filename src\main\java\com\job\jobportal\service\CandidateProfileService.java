package com.job.jobportal.service;

import com.job.jobportal.dto.*;
import com.job.jobportal.model.CandidateProfile;
import com.job.jobportal.model.MasterData;
import com.job.jobportal.model.Registereduser;
import com.job.jobportal.model.Roles;
import com.job.jobportal.repository.CandidateProfileRepo;
import com.job.jobportal.repository.MasterDataRepository;
import com.job.jobportal.repository.RegisteruserRepository;
import com.job.jobportal.repository.RolesRepository;
import com.job.jobportal.security.TokenProvider;
import com.job.jobportal.security.UserPrincipal;
import com.job.jobportal.util.CommonUtils;
import jakarta.servlet.http.HttpServletRequest;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class CandidateProfileService {
    @Autowired
    private CandidateProfileRepo candidateProfileRepo;
    @Autowired
    private RegisteruserRepository registeruserRepository;
    @Autowired
    private TokenProvider tokenProvider;
    @Autowired
    private ModelMapper modelMapper;
    @Autowired
    private MessageSource messageSource;
    @Autowired
    private MasterDataRepository masterDataRepository;
    @Autowired
    private RolesRepository rolesRepository;
    private static final Logger logger = LoggerFactory.getLogger(CandidateProfileService.class);
    private static final int JOB_CATEGORIES_COMPONENT_TYPE_ID = 12;
    private static final int JOB_SUBCATEGORIES_COMPONENT_TYPE_ID = 17;
    private static final int JOB_SUBSUBCATEGORIES_COMPONENT_TYPE_ID = 23;
    private static final int SKILLS_COMPONENT_TYPE_ID = 1;

    public Registereduser getUserId() {
        return registeruserRepository.findById(CommonUtils.getUserPrincipal().getId())
                .orElseThrow(() -> new RuntimeException("Registered User not found"));
    }

    private void setCurrentUserEmail(CandidateProfileDTO dto) {
        Registereduser user = getUserId();
        dto.setCurrentUserEmail(Objects.requireNonNullElse(user.getEmail(), user.getUsername()));
    }

    private boolean hasRole(Registereduser user) {
        return user.getRoles().stream()
                .map(Roles::getRolename)
                .anyMatch("CANDIDATE"::equals);
    }

    public CandidateProfileDTO getCandidateProfileByUser(HttpServletRequest request) {
        final String token = request.getHeader("Authorization").replace("Bearer ", "");
        final Registereduser user = getUserId();

        if (!hasRole(user)) {
            throw new IllegalStateException(messageSource.getMessage("msg.role_mismatch", null, Locale.getDefault()));
        }

        final CandidateProfile profile = candidateProfileRepo.findByRegistereduser_Userid(user.getUserid())
                .orElse(null);

        if (profile == null) {
            CandidateProfileDTO emptyDto = new CandidateProfileDTO();
            emptyDto.setUserId(user.getUserid());
            setCurrentUserEmail(emptyDto);
            return emptyDto;
        }

        CandidateProfileDTO responseDto = modelMapper.map(profile, CandidateProfileDTO.class);
        responseDto.setUserId(user.getUserid());

        logger.info("District field mapped in getCandidateProfileByUser: {}", responseDto.getDistrict());

        if (profile.getSkills() != null && !profile.getSkills().isEmpty()) {
            List<String> skillsList = Arrays.asList(profile.getSkills().split(","));
            List<MasterData> skillsData = masterDataRepository
                    .findByComponentType_IdIn(Collections.singletonList(SKILLS_COMPONENT_TYPE_ID))
                    .stream()
                    .filter(md -> skillsList.contains(md.getValue()))
                    .toList();

            responseDto.setSkillsNames(skillsList);
            responseDto.setSkillIds(skillsData.stream()
                    .map(MasterData::getMasterDataId)
                    .collect(Collectors.toList()));
        }

        if (profile.getJobCategories() != null && !profile.getJobCategories().isEmpty()) {
            MasterData categoryData = masterDataRepository
                    .findByComponentType_IdIn(Collections.singletonList(JOB_CATEGORIES_COMPONENT_TYPE_ID))
                    .stream()
                    .filter(md -> profile.getJobCategories().equals(md.getValue()))
                    .findFirst()
                    .orElse(null);

            if (categoryData != null) {
                responseDto.setJobCategoryName(profile.getJobCategories());
                responseDto.setJobCategories(String.valueOf(categoryData.getMasterDataId()));
            }
        }

        if (profile.getJobSubCategories() != null && !profile.getJobSubCategories().isEmpty()) {
            MasterData subCategoryData = masterDataRepository
                    .findByComponentType_IdIn(Collections.singletonList(JOB_SUBCATEGORIES_COMPONENT_TYPE_ID))
                    .stream()
                    .filter(md -> profile.getJobSubCategories().equals(md.getValue()))
                    .findFirst()
                    .orElse(null);

            if (subCategoryData != null) {
                String value = profile.getJobSubCategories();
                if (value.contains("|")) {
                    responseDto.setJobSubCategoryName(value.split("\\|")[0]);
                } else {
                    responseDto.setJobSubCategoryName(value);
                }
                responseDto.setJobSubCategories(String.valueOf(subCategoryData.getMasterDataId()));
            }
        }

        if (profile.getJobSubSubCategories() != null && !profile.getJobSubSubCategories().isEmpty()) {
            MasterData subSubCategoryData = masterDataRepository
                    .findByComponentType_IdIn(Collections.singletonList(JOB_SUBSUBCATEGORIES_COMPONENT_TYPE_ID))
                    .stream()
                    .filter(md -> profile.getJobSubSubCategories().equals(md.getValue()))
                    .findFirst()
                    .orElse(null);

            if (subSubCategoryData != null) {
                String value = profile.getJobSubSubCategories();
                if (value.contains("|")) {
                    responseDto.setJobSubSubCategoryName(value.split("\\|")[0]);
                } else {
                    responseDto.setJobSubSubCategoryName(value);
                }
                responseDto.setJobSubSubCategories(String.valueOf(subSubCategoryData.getMasterDataId()));
            }
        }

        if (profile.getExperience() != null) {
            MasterData experience = masterDataRepository.findByComponentType_IdAndMasterDataId(5,
                    Integer.parseInt(profile.getExperience()));
            if (experience != null) {
                responseDto.setExperience(profile.getExperience());
                responseDto.setExperienceName(experience.getValue());
            }
        }

        setCurrentUserEmail(responseDto);

        return responseDto;
    }


    public Page<CandidateProfile> getAllCandidates(
            Boolean isActive,
            String location,
            String skills,
            Integer yearsOfExperience,
            String addressCountry,
            String sortBy,
            String sortDirection,
            int page,
            int size) {
        Sort.Direction direction = Sort.Direction.fromString(sortDirection);
        Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));
        return candidateProfileRepo.findByFilters(isActive, location, skills, yearsOfExperience, addressCountry, pageable);
    }

    private void validateSkills(Set<Integer> skillIds) {
        if (skillIds == null || skillIds.isEmpty()) {
            logger.info("No skills provided for validation");
            return;
        }

        List<MasterData> validSkills = masterDataRepository.findByComponentType_IdIn(Collections.singletonList(SKILLS_COMPONENT_TYPE_ID));
        Set<Integer> validIds = validSkills.stream()
                .map(MasterData::getMasterDataId)
                .collect(Collectors.toSet());

        Set<Integer> invalidSkills = skillIds.stream()
                .filter(id -> !validIds.contains(id))
                .collect(Collectors.toSet());

        if (!invalidSkills.isEmpty()) {
            logger.warn("Invalid skills provided: {}", invalidSkills);
            throw new RuntimeException("Invalid skills provided: " + invalidSkills);
        }
    }

    private void setSkills(CandidateProfile profile, Set<Integer> skillIds) {
        List<MasterData> skills = masterDataRepository.findByComponentType_IdIn(Collections.singletonList(SKILLS_COMPONENT_TYPE_ID))
                .stream()
                .filter(md -> skillIds.contains(md.getMasterDataId()))
                .toList();

        String skillsString = skills.stream()
                .map(MasterData::getValue)
                .collect(Collectors.joining(","));

        profile.setSkills(skillsString);
    }

    private List<Integer> getSkillIds(List<String> skillNames) {
        if (skillNames == null || skillNames.isEmpty()) {
            return new ArrayList<>();
        }

        return masterDataRepository.findByComponentType_IdIn(Collections.singletonList(SKILLS_COMPONENT_TYPE_ID))
                .stream()
                .filter(md -> skillNames.contains(md.getValue()))
                .map(MasterData::getMasterDataId)
                .collect(Collectors.toList());
    }

    private void validateJobCategory(Integer categoryId) {
        if (categoryId == null) {
            logger.info("No job category provided for validation");
            return;
        }

        List<MasterData> validCategories = masterDataRepository.findByComponentType_IdIn(
                Collections.singletonList(JOB_CATEGORIES_COMPONENT_TYPE_ID)
        );
        Set<Integer> validCategoryIds = validCategories.stream()
                .map(MasterData::getMasterDataId)
                .collect(Collectors.toSet());

        if (!validCategoryIds.contains(categoryId)) {
            logger.warn("Invalid job category provided: {}", categoryId);
            throw new IllegalArgumentException("Invalid job category provided: " + categoryId);
        }
    }

    private void validateJobSubCategory(Integer subCategoryId) {
        if (subCategoryId == null) {
            logger.info("No job subcategory provided for validation");
            return;
        }

        MasterData subCategoryData = masterDataRepository
                .findByComponentType_IdAndMasterDataId(JOB_SUBCATEGORIES_COMPONENT_TYPE_ID, subCategoryId);

        if (subCategoryData == null) {
            logger.warn("Invalid job subcategory provided: {}", subCategoryId);
            throw new IllegalArgumentException("Invalid job subcategory provided: " + subCategoryId);
        }
    }

    private void validateJobSubCategories(Set<Integer> subCategoryIds) {
        if (subCategoryIds == null || subCategoryIds.isEmpty()) {
            logger.info("No job subcategories provided for validation");
            return;
        }

        Integer subCategoryId = subCategoryIds.iterator().next();
        validateJobSubCategory(subCategoryId);
    }

    private void validateJobSubSubCategory(Integer subSubCategoryId) {
        if (subSubCategoryId == null) {
            logger.info("No job sub-subcategory provided for validation");
            return;
        }

        MasterData subSubCategoryData = masterDataRepository
                .findByComponentType_IdAndMasterDataId(JOB_SUBSUBCATEGORIES_COMPONENT_TYPE_ID, subSubCategoryId);

        if (subSubCategoryData == null) {
            logger.warn("Invalid job sub-subcategory provided: {}", subSubCategoryId);
            throw new IllegalArgumentException("Invalid job sub-subcategory provided: " + subSubCategoryId);
        }
    }

    private void validateJobSubSubCategories(Set<Integer> subSubCategoryIds) {
        if (subSubCategoryIds == null || subSubCategoryIds.isEmpty()) {
            logger.info("No job sub-subcategories provided for validation");
            return;
        }

        Integer subSubCategoryId = subSubCategoryIds.iterator().next();
        validateJobSubSubCategory(subSubCategoryId);
    }

    private void setJobCategory(CandidateProfile profile, Integer categoryId) {
        if (categoryId != null) {
            MasterData categoryData = masterDataRepository
                    .findByComponentType_IdAndMasterDataId(JOB_CATEGORIES_COMPONENT_TYPE_ID, categoryId);

            if (categoryData != null) {
                profile.setJobCategories(categoryData.getValue());
            }
        }
    }

    private void setJobSubCategory(CandidateProfile profile, Integer subCategoryId) {
        if (subCategoryId != null) {
            MasterData subCategoryData = masterDataRepository
                    .findByComponentType_IdAndMasterDataId(JOB_SUBCATEGORIES_COMPONENT_TYPE_ID, subCategoryId);

            if (subCategoryData != null) {
                profile.setJobSubCategories(subCategoryData.getValue());
            }
        }
    }

    private void setJobSubSubCategory(CandidateProfile profile, Integer subSubCategoryId) {
        if (subSubCategoryId != null) {
            MasterData subSubCategoryData = masterDataRepository
                    .findByComponentType_IdAndMasterDataId(JOB_SUBSUBCATEGORIES_COMPONENT_TYPE_ID, subSubCategoryId);

            if (subSubCategoryData != null) {
                profile.setJobSubSubCategories(subSubCategoryData.getValue());
            }
        }
    }

    private void setJobSubCategories(CandidateProfile profile, Set<Integer> subCategoryIds) {
        if (subCategoryIds != null && !subCategoryIds.isEmpty()) {
            Integer subCategoryId = subCategoryIds.iterator().next();
            setJobSubCategory(profile, subCategoryId);
        }
    }

    private void setJobSubSubCategories(CandidateProfile profile, Set<Integer> subSubCategoryIds) {
        if (subSubCategoryIds != null && !subSubCategoryIds.isEmpty()) {
            Integer subSubCategoryId = subSubCategoryIds.iterator().next();
            setJobSubSubCategory(profile, subSubCategoryId);
        }
    }

    @Transactional
    public CandidateProfileResponseDTO createCandidateProfile(CandidateProfileDTO dto) {
        validateCandidateProfileDTO(dto);
        validateSkills(new HashSet<>(dto.getSkillIds()));
        validateJobCategory(Integer.valueOf(dto.getJobCategories()));

        if (dto.getJobSubCategories() != null && !dto.getJobSubCategories().isEmpty()) {
            validateJobSubCategory(Integer.valueOf(dto.getJobSubCategories()));
        }

        if (dto.getJobSubSubCategories() != null && !dto.getJobSubSubCategories().isEmpty()) {
            validateJobSubSubCategory(Integer.valueOf(dto.getJobSubSubCategories()));
        }

        Registereduser user = getUserId();

        if (!hasRole(user)) {
            throw new IllegalStateException("Role mismatch - Candidate role required");
        }

        Optional<CandidateProfile> existingProfileOpt = candidateProfileRepo.findByRegistereduser_Userid(user.getUserid());
        if (existingProfileOpt.isPresent()) {
            logger.info("Candidate profile already exists for user ID: {}. Redirecting to update logic.", user.getUserid());

            dto.setCandidateProfileId(existingProfileOpt.get().getCandidateProfileId());

            CandidateProfileResponseDTO updatedResponse = updateCandidateProfile(dto);

            logger.info("Updated existing candidate profile with ID: {} instead of creating a new one", updatedResponse.getProfile().getCandidateProfileId());

            return updatedResponse;
        }

        if (dto.getExperience() != null) {
            MasterData experience = masterDataRepository.findByComponentType_IdAndMasterDataId(5,
                    Integer.parseInt(dto.getExperience()));
            if (experience == null) {
                throw new IllegalArgumentException("Invalid experience value provided");
            }
            dto.setExperienceName(experience.getValue());
        }

        CandidateProfile profile = modelMapper.map(dto, CandidateProfile.class);

        if (dto.getDistrict() != null) {
            profile.setDistrict(dto.getDistrict());
            logger.info("Set district to: {} for new candidate profile", dto.getDistrict());
        }

        if (dto.getLocation() != null) {
            try {
                int locationId = Integer.parseInt(dto.getLocation());
                MasterData locationData = masterDataRepository.findByComponentType_IdAndMasterDataId(3, locationId);
                if (locationData != null) {
                    profile.setLocation(locationData.getValue());
                    logger.info("Converted location ID {} to value: {}", locationId, locationData.getValue());
                }
            } catch (NumberFormatException e) {
                logger.debug("Location is not an ID, storing as is: {}", dto.getLocation());
            }
        }
        profile.setRegistereduser(user);
        profile.setIsActive(true);

        setSkills(profile, new HashSet<>(dto.getSkillIds()));
        setJobCategory(profile, Integer.valueOf(dto.getJobCategories()));

        if (dto.getJobSubCategories() != null && !dto.getJobSubCategories().isEmpty()) {
            setJobSubCategory(profile, Integer.valueOf(dto.getJobSubCategories()));
        }

        if (dto.getJobSubSubCategories() != null && !dto.getJobSubSubCategories().isEmpty()) {
            setJobSubSubCategory(profile, Integer.valueOf(dto.getJobSubSubCategories()));
        }

        CandidateProfile savedProfile = candidateProfileRepo.save(profile);

        boolean tokenNeedsUpdate = !user.isHasCandidateProfile();
        user.setHasCandidateProfile(true);
        registeruserRepository.save(user);
        logger.info("Set hasCandidateProfile flag to true for user ID: {}", user.getUserid());

        String newToken = null;
        if (tokenNeedsUpdate) {
            UserPrincipal refreshedPrincipal = CommonUtils.refreshUserPrincipal(user.getUserid());
            newToken = tokenProvider.createToken(refreshedPrincipal);
            logger.info("Generated new token with updated hasCandidateProfile flag for user ID: {}", user.getUserid());
        }

        CandidateProfileDTO profileDto = modelMapper.map(savedProfile, CandidateProfileDTO.class);
        profileDto.setUserId(user.getUserid());

        List<MasterData> skillsData = masterDataRepository
                .findByComponentType_IdIn(Collections.singletonList(SKILLS_COMPONENT_TYPE_ID))
                .stream()
                .filter(md -> dto.getSkillIds().contains(md.getMasterDataId()))
                .toList();

        profileDto.setSkillIds(dto.getSkillIds());
        profileDto.setSkillsNames(skillsData.stream()
                .map(MasterData::getValue)
                .collect(Collectors.toList()));

        if (dto.getJobCategories() != null) {
            MasterData categoryData = masterDataRepository
                    .findByComponentType_IdAndMasterDataId(JOB_CATEGORIES_COMPONENT_TYPE_ID, Integer.valueOf(dto.getJobCategories()));

            if (categoryData != null) {
                profileDto.setJobCategories(dto.getJobCategories());
                profileDto.setJobCategoryName(categoryData.getValue());
            }
        }

        if (dto.getJobSubCategories() != null && !dto.getJobSubCategories().isEmpty()) {
            MasterData subCategoryData = masterDataRepository
                    .findByComponentType_IdAndMasterDataId(JOB_SUBCATEGORIES_COMPONENT_TYPE_ID, Integer.valueOf(dto.getJobSubCategories()));

            if (subCategoryData != null) {
                profileDto.setJobSubCategories(dto.getJobSubCategories());
                String value = subCategoryData.getValue();
                if (value != null && value.contains("|")) {
                    profileDto.setJobSubCategoryName(value.split("\\|")[0]);
                } else {
                    profileDto.setJobSubCategoryName(value);
                }
            }
        }

        if (dto.getJobSubSubCategories() != null && !dto.getJobSubSubCategories().isEmpty()) {
            MasterData subSubCategoryData = masterDataRepository
                    .findByComponentType_IdAndMasterDataId(JOB_SUBSUBCATEGORIES_COMPONENT_TYPE_ID, Integer.valueOf(dto.getJobSubSubCategories()));

            if (subSubCategoryData != null) {
                profileDto.setJobSubSubCategories(dto.getJobSubSubCategories());
                String value = subSubCategoryData.getValue();
                if (value != null && value.contains("|")) {
                    profileDto.setJobSubSubCategoryName(value.split("\\|")[0]);
                } else {
                    profileDto.setJobSubSubCategoryName(value);
                }
            }
        }

        if (dto.getExperience() != null) {
            MasterData experience = masterDataRepository.findByComponentType_IdAndMasterDataId(5,
                    Integer.parseInt(dto.getExperience()));
            profileDto.setExperience(dto.getExperience());
            profileDto.setExperienceName(experience.getValue());
        }

        setCurrentUserEmail(profileDto);
        logger.info("Created candidate profile for user ID: {}", profileDto.getUserId());

        return newToken != null
            ? CandidateProfileResponseDTO.fromProfileAndToken(profileDto, newToken)
            : CandidateProfileResponseDTO.fromProfile(profileDto);
    }

    public CandidateProfile createCandidateProfileFromRequest(JobApplicationRequest request) {
        Optional<Registereduser> existingUser = registeruserRepository.findByEmail(request.getEmail());
        Registereduser user;

        if (existingUser.isPresent()) {
            user = existingUser.get();

            if (user.isHasCandidateProfile()) {
                Optional<CandidateProfile> existingProfile = candidateProfileRepo.findByRegistereduser_Userid(user.getUserid());
                if (existingProfile.isPresent()) {
                    logger.info("Using existing candidate profile for user ID: {}", user.getUserid());
                    return existingProfile.get();
                }
            }

            boolean hasCandidateRole = user.getRoles().stream()
                    .anyMatch(role -> role.getRolename().equals("CANDIDATE"));

            if (!hasCandidateRole) {
                Roles candidateRole = rolesRepository.findByRolename("CANDIDATE");
                user.getRoles().add(candidateRole);
                registeruserRepository.save(user);
                logger.info("Added CANDIDATE role to existing user ID: {}", user.getUserid());
            }
        } else {
            user = new Registereduser();
            user.setFirstname(request.getFullName());
            user.setMobileno(request.getMobileNumber());
            user.setEmail(request.getEmail());
            //user.setUsername(request.getEmail());

            registeruserRepository.save(user);

            Roles candidateRole = rolesRepository.findByRolename("CANDIDATE");
            Set<Roles> roles = new HashSet<>();
            roles.add(candidateRole);
            user.setRoles(roles);
            registeruserRepository.save(user);
            logger.info("Created new user with ID: {}", user.getUserid());
        }

        CandidateProfile profile = new CandidateProfile();
        profile.setRegistereduser(user);

        profile.setFullName(request.getFullName());
        profile.setPhoneNumber(user.getMobileno());
        profile.setResumeUrl(request.getResumeUrl());

        profile.setSkills("");
        profile.setJobCategories("");
        profile.setJobSubCategories("");
        profile.setJobSubSubCategories("");
        profile.setYearsOfExperience("");
        profile.setIsActive(true);
        profile.setDistrict("");

        CandidateProfile savedProfile = candidateProfileRepo.save(profile);
        logger.info("Created new candidate profile with basic information for user ID: {}", user.getUserid());
        return savedProfile;
    }

    @Transactional
    public CandidateProfileResponseDTO updateCandidateProfile(CandidateProfileDTO dto) {
        logger.info("Starting candidate profile update process");
        validateCandidateProfileDTO(dto);

        if (dto.getSkillIds() == null) {
            dto.setSkillIds(new ArrayList<>());
            logger.info("Initialized empty skillIds list");
        }


        validateSkills(new HashSet<>(dto.getSkillIds()));
        validateJobCategory(Integer.valueOf(dto.getJobCategories()));

        if (dto.getJobSubCategories() != null && !dto.getJobSubCategories().isEmpty()) {
            validateJobSubCategory(Integer.valueOf(dto.getJobSubCategories()));
        }

        if (dto.getJobSubSubCategories() != null && !dto.getJobSubSubCategories().isEmpty()) {
            validateJobSubSubCategory(Integer.valueOf(dto.getJobSubSubCategories()));
        }

        Registereduser user = getUserId();
        if (!hasRole(user)) {
            logger.warn("User {} does not have the CANDIDATE role", user.getUserid());
            throw new IllegalStateException(
                    messageSource.getMessage("msg.role_mismatch", null, Locale.getDefault())
            );
        }

        CandidateProfile profile = candidateProfileRepo.findByRegistereduser_Userid(user.getUserid())
                .orElseThrow(() -> {
                    logger.error("Candidate profile not found for user ID: {}", user.getUserid());
                    return new RuntimeException(
                            messageSource.getMessage("msg.candidate_profile_not_found", null, Locale.getDefault())
                    );
                });

        if (!dto.getSkillIds().isEmpty()) {
            setSkills(profile, new HashSet<>(dto.getSkillIds()));
            logger.info("Updated skills for profile ID: {}", profile.getCandidateProfileId());
        }

        if (dto.getJobCategories() != null) {
            setJobCategory(profile, Integer.valueOf(dto.getJobCategories()));
            logger.info("Updated job category for profile ID: {}", profile.getCandidateProfileId());
        }

        if (dto.getJobSubCategories() != null && !dto.getJobSubCategories().isEmpty()) {
            setJobSubCategory(profile, Integer.valueOf(dto.getJobSubCategories()));
            logger.info("Updated job subcategory for profile ID: {}", profile.getCandidateProfileId());
        }

        if (dto.getJobSubSubCategories() != null && !dto.getJobSubSubCategories().isEmpty()) {
            setJobSubSubCategory(profile, Integer.valueOf(dto.getJobSubSubCategories()));
            logger.info("Updated job sub-subcategory for profile ID: {}", profile.getCandidateProfileId());
        }

        updateProfileFields(profile, dto);
        logger.info("Updated profile fields for profile ID: {}", profile.getCandidateProfileId());

        CandidateProfile updatedProfile = candidateProfileRepo.save(profile);
        logger.info("Saved updated profile with ID: {}", updatedProfile.getCandidateProfileId());

        boolean tokenNeedsUpdate = !user.isHasCandidateProfile();
        if (tokenNeedsUpdate) {
            user.setHasCandidateProfile(true);
            registeruserRepository.save(user);
            logger.info("Set hasCandidateProfile flag to true for user ID: {}", user.getUserid());
        }

        String newToken = null;
        if (tokenNeedsUpdate) {
            UserPrincipal refreshedPrincipal = CommonUtils.refreshUserPrincipal(user.getUserid());
            newToken = tokenProvider.createToken(refreshedPrincipal);
            logger.info("Generated new token with updated hasCandidateProfile flag for user ID: {}", user.getUserid());
        }

        CandidateProfileDTO profileDto = modelMapper.map(updatedProfile, CandidateProfileDTO.class);
        profileDto.setUserId(user.getUserid());

        if (profile.getSkills() != null && !profile.getSkills().isEmpty()) {
            List<String> skillsList = Arrays.asList(profile.getSkills().split(","));
            List<MasterData> skillsData = masterDataRepository
                    .findByComponentType_IdIn(Collections.singletonList(SKILLS_COMPONENT_TYPE_ID))
                    .stream()
                    .filter(md -> skillsList.contains(md.getValue()))
                    .toList();

            profileDto.setSkillsNames(skillsList);
            profileDto.setSkillIds(skillsData.stream()
                    .map(MasterData::getMasterDataId)
                    .collect(Collectors.toList()));
            logger.info("Set {} skills in response", skillsList.size());
        } else {
            profileDto.setSkillsNames(new ArrayList<>());
            profileDto.setSkillIds(new ArrayList<>());
            logger.info("No skills to set in response");
        }

        if (profile.getJobCategories() != null && !profile.getJobCategories().isEmpty()) {
            MasterData categoryData = masterDataRepository
                    .findByComponentType_IdIn(Collections.singletonList(JOB_CATEGORIES_COMPONENT_TYPE_ID))
                    .stream()
                    .filter(md -> profile.getJobCategories().equals(md.getValue()))
                    .findFirst()
                    .orElse(null);

            if (categoryData != null) {
                profileDto.setJobCategoryName(profile.getJobCategories());
                profileDto.setJobCategories(String.valueOf(categoryData.getMasterDataId()));
                logger.info("Set job category in response: {}", profile.getJobCategories());
            }
        } else {
            profileDto.setJobCategoryName(null);
            profileDto.setJobCategories(null);
            logger.info("No job category to set in response");
        }

        if (profile.getJobSubCategories() != null && !profile.getJobSubCategories().isEmpty()) {
            MasterData subCategoryData = masterDataRepository
                    .findByComponentType_IdIn(Collections.singletonList(JOB_SUBCATEGORIES_COMPONENT_TYPE_ID))
                    .stream()
                    .filter(md -> profile.getJobSubCategories().equals(md.getValue()))
                    .findFirst()
                    .orElse(null);

            if (subCategoryData != null) {
                String value = profile.getJobSubCategories();
                if (value.contains("|")) {
                    profileDto.setJobSubCategoryName(value.split("\\|")[0]);
                } else {
                    profileDto.setJobSubCategoryName(value);
                }
                profileDto.setJobSubCategories(String.valueOf(subCategoryData.getMasterDataId()));
                logger.info("Set job subcategory in response: {}", value);
            }
        } else {
            profileDto.setJobSubCategoryName(null);
            profileDto.setJobSubCategories(null);
            logger.info("No job subcategory to set in response");
        }

        if (profile.getJobSubSubCategories() != null && !profile.getJobSubSubCategories().isEmpty()) {
            MasterData subSubCategoryData = masterDataRepository
                    .findByComponentType_IdIn(Collections.singletonList(JOB_SUBSUBCATEGORIES_COMPONENT_TYPE_ID))
                    .stream()
                    .filter(md -> profile.getJobSubSubCategories().equals(md.getValue()))
                    .findFirst()
                    .orElse(null);

            if (subSubCategoryData != null) {
                String value = profile.getJobSubSubCategories();
                if (value.contains("|")) {
                    profileDto.setJobSubSubCategoryName(value.split("\\|")[0]);
                } else {
                    profileDto.setJobSubSubCategoryName(value);
                }
                profileDto.setJobSubSubCategories(String.valueOf(subSubCategoryData.getMasterDataId()));
                logger.info("Set job sub-subcategory in response: {}", value);
            }
        } else {
            profileDto.setJobSubSubCategoryName(null);
            profileDto.setJobSubSubCategories(null);
            logger.info("No job sub-subcategory to set in response");
        }

        profileDto.setCurrentUserEmail(user.getEmail());

        logger.info("Updated candidate profile with ID: {}", updatedProfile.getCandidateProfileId());

        return newToken != null
            ? CandidateProfileResponseDTO.fromProfileAndToken(profileDto, newToken)
            : CandidateProfileResponseDTO.fromProfile(profileDto);
    }

    @Transactional
    public CandidateProfileDTO updateCandidateStatus(Long userId, Long candidateProfileId, boolean isActive) {
        Registereduser user = getRegisteredUser(userId);
        CandidateProfile profile = candidateProfileRepo.findById(candidateProfileId)
                .orElseThrow(() -> new RuntimeException(
                        messageSource.getMessage("msg.candidate_profile_fetched", null, Locale.getDefault())
                ));
        profile.setIsActive(isActive);
        candidateProfileRepo.save(profile);

        CandidateProfileDTO candidateProfileDTO = modelMapper.map(profile, CandidateProfileDTO.class);

        if (profile.getJobCategories() != null && !profile.getJobCategories().isEmpty()) {
            MasterData categoryData = masterDataRepository
                    .findByComponentType_IdIn(Collections.singletonList(JOB_CATEGORIES_COMPONENT_TYPE_ID))
                    .stream()
                    .filter(md -> profile.getJobCategories().equals(md.getValue()))
                    .findFirst()
                    .orElse(null);

            if (categoryData != null) {
                candidateProfileDTO.setJobCategoryName(profile.getJobCategories());
                candidateProfileDTO.setJobCategories(String.valueOf(categoryData.getMasterDataId()));
            }
        }

        setCurrentUserEmail(candidateProfileDTO);

        logger.info("Updated status of candidate profile ID: {} to {}", candidateProfileId, isActive);
        return candidateProfileDTO;
    }

    private void updateProfileFields(CandidateProfile profile, CandidateProfileDTO dto) {
        if (dto.getFullName() != null) profile.setFullName(dto.getFullName());
        if (dto.getPhoneNumber() != null) profile.setPhoneNumber(dto.getPhoneNumber());
        if (dto.getResumeUrl() != null) profile.setResumeUrl(dto.getResumeUrl());
        if (dto.getWebsite() != null) profile.setWebsite(dto.getWebsite());
        if (dto.getJobTitle() != null) profile.setJobTitle(dto.getJobTitle());

        if (dto.getExpectedSalary() != null) profile.setExpectedSalary(dto.getExpectedSalary());
        if (dto.getSalaryCurrency() != null) profile.setSalaryCurrency(dto.getSalaryCurrency());
        if (dto.getCurrentSalary() != null) profile.setCurrentSalary(dto.getCurrentSalary());
        if (dto.getYearsOfExperience() != null) profile.setYearsOfExperience(dto.getYearsOfExperience());
        if (dto.getExperience() != null) profile.setExperience(dto.getExperience());
        if (dto.getDesignation() != null) profile.setDesignation(dto.getDesignation());

        if (dto.getEducation() != null) profile.setEducation(dto.getEducation());

        if (dto.getLocation() != null) {
            try {
                int locationId = Integer.parseInt(dto.getLocation());
                MasterData locationData = masterDataRepository.findByComponentType_IdAndMasterDataId(3, locationId);
                if (locationData != null) {
                    profile.setLocation(locationData.getValue());
                    logger.info("Converted location ID {} to value: {}", locationId, locationData.getValue());
                } else {
                    profile.setLocation(dto.getLocation());
                    logger.warn("Location ID {} not found in master data", locationId);
                }
            } catch (NumberFormatException e) {
                profile.setLocation(dto.getLocation());
                logger.debug("Location is not an ID, storing as is: {}", dto.getLocation());
            }
        }
        if (dto.getAddressCountry() != null) profile.setAddressCountry(dto.getAddressCountry());
        if (dto.getAddressCity() != null) profile.setAddressCity(dto.getAddressCity());
        if (dto.getDistrict() != null) profile.setDistrict(dto.getDistrict());
        if (dto.getAddressLineOne() != null) profile.setAddressLineOne(dto.getAddressLineOne());
        if (dto.getAddressLineTwo() != null) profile.setAddressLineTwo(dto.getAddressLineTwo());
        if (dto.getAddressPincode() != null) profile.setAddressPincode(dto.getAddressPincode());
        if (dto.getCandidateAddressMapLocationLattitude() != null) profile.setCandidateAddressMapLocationLattitude(dto.getCandidateAddressMapLocationLattitude());
        if (dto.getCandidateAddressMapLocationLongtitude() != null) profile.setCandidateAddressMapLocationLongtitude(dto.getCandidateAddressMapLocationLongtitude());

        if (dto.getSocialLinkedIn() != null) profile.setSocialLinkedIn(dto.getSocialLinkedIn());
        if (dto.getSocialFacebook() != null) profile.setSocialFacebook(dto.getSocialFacebook());
        if (dto.getSocialTwitter() != null) profile.setSocialTwitter(dto.getSocialTwitter());
        if (dto.getSocialGlassDoor() != null) profile.setSocialGlassDoor(dto.getSocialGlassDoor());

        if (dto.getProfilePictureURL() != null) profile.setProfilePictureURL(dto.getProfilePictureURL());
        if (dto.getProfilePictureKey() != null) profile.setProfilePictureKey(dto.getProfilePictureKey());

        if (dto.getDescription() != null) profile.setDescription(dto.getDescription());
        if (dto.getLanguage() != null) profile.setLanguage(dto.getLanguage());
        if (dto.getIsActive() != null) profile.setIsActive(dto.getIsActive());

        logger.info("Updated all available fields for profile ID: {}", profile.getCandidateProfileId());
    }

    private void validateCandidateProfileDTO(CandidateProfileDTO dto) {

        if (dto.getFullName() != null && dto.getFullName().trim().isEmpty()) {
            throw new IllegalArgumentException("Full name cannot be empty if provided");
        }

        if (dto.getPhoneNumber() != null && dto.getPhoneNumber().trim().isEmpty()) {
            throw new IllegalArgumentException("Phone number cannot be empty if provided");
        }

        if (dto.getYearsOfExperience() != null && dto.getYearsOfExperience().trim().isEmpty()) {
            throw new IllegalArgumentException("Years of experience cannot be empty if provided");
        }

        if (dto.getExpectedSalary() != null && dto.getExpectedSalary() < 0) {
            throw new IllegalArgumentException("Expected salary cannot be negative");
        }

        if (dto.getCurrentSalary() != null && dto.getCurrentSalary() < 0) {
            throw new IllegalArgumentException("Current salary cannot be negative");
        }

        logger.info("Candidate profile DTO validation passed");
    }

    private Registereduser getRegisteredUser(Long userId) {
        return registeruserRepository.findById(userId)
                .orElseThrow(() -> new IllegalStateException(
                        messageSource.getMessage("msg.user_not_found", null, Locale.getDefault())
                ));
    }

    public CandidateProfileDTO getCandidateProfileById(Long candidateProfileId) {
        CandidateProfile profile = candidateProfileRepo.findById(candidateProfileId)
                .orElseThrow(() -> new RuntimeException(
                        messageSource.getMessage("msg.candidate_profile_not_found", null, Locale.getDefault())
                ));

        CandidateProfileDTO responseDto = modelMapper.map(profile, CandidateProfileDTO.class);
        responseDto.setUserId(profile.getRegistereduser().getUserid());

        logger.info("District field mapped in getCandidateProfileById: {}", responseDto.getDistrict());

        if (profile.getSkills() != null && !profile.getSkills().isEmpty()) {
            List<String> skillsList = Arrays.asList(profile.getSkills().split(","));
            List<MasterData> skillsData = masterDataRepository
                    .findByComponentType_IdIn(Collections.singletonList(SKILLS_COMPONENT_TYPE_ID))
                    .stream()
                    .filter(md -> skillsList.contains(md.getValue()))
                    .toList();

            responseDto.setSkillsNames(skillsList);
            responseDto.setSkillIds(skillsData.stream()
                    .map(MasterData::getMasterDataId)
                    .collect(Collectors.toList()));
        }

        if (profile.getJobCategories() != null && !profile.getJobCategories().isEmpty()) {
            MasterData categoryData = masterDataRepository
                    .findByComponentType_IdIn(Collections.singletonList(JOB_CATEGORIES_COMPONENT_TYPE_ID))
                    .stream()
                    .filter(md -> profile.getJobCategories().equals(md.getValue()))
                    .findFirst()
                    .orElse(null);

            if (categoryData != null) {
                responseDto.setJobCategoryName(profile.getJobCategories());
                responseDto.setJobCategories(String.valueOf(categoryData.getMasterDataId()));
            }
        }

        if (profile.getJobSubCategories() != null && !profile.getJobSubCategories().isEmpty()) {
            MasterData subCategoryData = masterDataRepository
                    .findByComponentType_IdIn(Collections.singletonList(JOB_SUBCATEGORIES_COMPONENT_TYPE_ID))
                    .stream()
                    .filter(md -> profile.getJobSubCategories().equals(md.getValue()))
                    .findFirst()
                    .orElse(null);

            if (subCategoryData != null) {
                String value = profile.getJobSubCategories();
                if (value.contains("|")) {
                    responseDto.setJobSubCategoryName(value.split("\\|")[0]);
                } else {
                    responseDto.setJobSubCategoryName(value);
                }
                responseDto.setJobSubCategories(String.valueOf(subCategoryData.getMasterDataId()));
            }
        }

        if (profile.getJobSubSubCategories() != null && !profile.getJobSubSubCategories().isEmpty()) {
            MasterData subSubCategoryData = masterDataRepository
                    .findByComponentType_IdIn(Collections.singletonList(JOB_SUBSUBCATEGORIES_COMPONENT_TYPE_ID))
                    .stream()
                    .filter(md -> profile.getJobSubSubCategories().equals(md.getValue()))
                    .findFirst()
                    .orElse(null);

            if (subSubCategoryData != null) {
                String value = profile.getJobSubSubCategories();
                if (value.contains("|")) {
                    responseDto.setJobSubSubCategoryName(value.split("\\|")[0]);
                } else {
                    responseDto.setJobSubSubCategoryName(value);
                }
                responseDto.setJobSubSubCategories(String.valueOf(subSubCategoryData.getMasterDataId()));
            }
        }

        if (profile.getExperience() != null) {
            MasterData experience = masterDataRepository.findByComponentType_IdAndMasterDataId(5,
                    Integer.parseInt(profile.getExperience()));
            if (experience != null) {
                responseDto.setExperience(profile.getExperience());
                responseDto.setExperienceName(experience.getValue());
            }
        }

        Registereduser user = profile.getRegistereduser();
        responseDto.setCurrentUserEmail(Objects.requireNonNullElse(user.getEmail(), user.getUsername()));

        logger.info("Retrieved candidate profile with ID: {}", candidateProfileId);
        return responseDto;
    }
}