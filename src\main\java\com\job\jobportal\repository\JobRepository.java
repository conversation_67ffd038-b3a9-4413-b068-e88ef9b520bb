package com.job.jobportal.repository;

import com.job.jobportal.dto.CityStateDTO;
import com.job.jobportal.model.JobPost;
import com.job.jobportal.model.JobStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;

@Repository
public interface JobRepository extends JpaRepository<JobPost, Long> {


/*    @Query("SELECT DISTINCT j FROM JobPost j " +
            "LEFT JOIN FETCH j.companyProfile " +
            "LEFT JOIN FETCH j.postedBy " +
            "LEFT JOIN j.keywords k " +*/

    long countByCompanyProfile_CompanyProfileId(Long companyProfileId);

    long countByPostedBy_Userid(Long userId);

    @Query("SELECT j FROM JobPost j " +

            "WHERE (:status IS NULL OR j.status = :status) " +
            "AND (:location IS NULL OR LOWER(j.location) LIKE LOWER(CONCAT('%', :location, '%'))) " +
            "AND (:salary IS NULL OR j.minSalary >= :salary) " +
            "AND (:jobType IS NULL OR j.jobType = :jobType) " +
            "AND (:careerLevel IS NULL OR j.careerLevel = :careerLevel) " +
            "AND (:gender IS NULL OR j.gender = :gender) " +
            "AND (:industry IS NULL OR j.industry = :industry) " +
            "AND (:qualification IS NULL OR j.qualification = :qualification) " +
            "AND (:jobTitle IS NULL OR LOWER(j.jobTitle) LIKE LOWER(CONCAT('%', :jobTitle, '%'))) " +
            "AND (:specialisms IS NULL OR " +
            "      EXISTS (SELECT 1 WHERE " +
            "              :specialisms IS NOT NULL AND " +
            "              LOWER(j.specialisms) LIKE LOWER(CONCAT('%', :specialisms, '%')))) " +
            "AND (:experience IS NULL OR j.experience = :experience) " +
            "AND (:country IS NULL OR LOWER(j.country) LIKE LOWER(CONCAT('%', :country, '%'))) " +
            "AND (:city IS NULL OR LOWER(j.city) LIKE LOWER(CONCAT('%', :city, '%'))) " +
            "AND (:category IS NULL OR j.jobCategory = CAST(:category AS string)) " +
            "AND (:subCategory IS NULL OR j.jobSubCategory = CAST(:subCategory AS string)) " +
            "AND (:subSubCategory IS NULL OR j.jobSubSubCategory = CAST(:subSubCategory AS string)) " +
            "AND (:searchTerm IS NULL OR " +
            "     LOWER(j.jobTitle) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
            "     EXISTS (SELECT 1 FROM j.keywords k WHERE LOWER(k) LIKE LOWER(CONCAT('%', :searchTerm, '%')))) " +
            "AND (j.status = 'ACTIVE') " +
            "AND (j.jobOpeningDate IS NULL OR j.jobOpeningDate <= CURRENT_TIMESTAMP) " +
            "AND (j.applicationDeadlineDate IS NULL OR j.applicationDeadlineDate > CURRENT_TIMESTAMP)")
    Page<JobPost> findJobsWithFilters(
            @Param("status") JobStatus status,
            @Param("location") String location,
            @Param("salary") Long salary,
            @Param("jobType") String jobType,
            @Param("careerLevel") String careerLevel,
            @Param("gender") String gender,
            @Param("industry") String industry,
            @Param("qualification") String qualification,
            @Param("jobTitle") String jobTitle,
            @Param("specialisms") String specialisms,
            @Param("experience") String experience,
            @Param("country") String country,
            @Param("city") String city,
            @Param("category") Integer category,
            @Param("subCategory") Integer subCategory,
            @Param("subSubCategory") Integer subSubCategory,
            @Param("searchTerm") String searchTerm,
            Pageable pageable);

    @Query("SELECT DISTINCT j FROM JobPost j " +
            "LEFT JOIN FETCH j.companyProfile " +
            "LEFT JOIN FETCH j.postedBy " +
            "LEFT JOIN j.keywords k " +
            "WHERE (:status IS NULL OR j.status = :status) " +
            "AND (:location IS NULL OR LOWER(j.location) LIKE LOWER(CONCAT('%', :location, '%'))) " +
            "AND (:salary IS NULL OR j.minSalary >= :salary) " +
            "AND (:jobType IS NULL OR j.jobType = :jobType) " +
            "AND (:careerLevel IS NULL OR j.careerLevel = :careerLevel) " +
            "AND (:gender IS NULL OR j.gender = :gender) " +
            "AND (:industry IS NULL OR j.industry = :industry) " +
            "AND (:qualification IS NULL OR j.qualification = :qualification) " +
            "AND (:jobTitle IS NULL OR LOWER(j.jobTitle) LIKE LOWER(CONCAT('%', :jobTitle, '%'))) " +
            "AND (:specialisms IS NULL OR " +
            "      EXISTS (SELECT 1 WHERE " +
            "              :specialisms IS NOT NULL AND " +
            "              LOWER(j.specialisms) LIKE LOWER(CONCAT('%', :specialisms, '%')))) " +
            "AND (:experience IS NULL OR j.experience = :experience) " +
            "AND (:country IS NULL OR LOWER(j.country) LIKE LOWER(CONCAT('%', :country, '%'))) " +
            "AND (:city IS NULL OR LOWER(j.city) LIKE LOWER(CONCAT('%', :city, '%'))) " +
            "AND (:category IS NULL OR j.jobCategory = CAST(:category AS string)) " +
            "AND (:subCategory IS NULL OR j.jobSubCategory = CAST(:subCategory AS string)) " +
            "AND (:subSubCategory IS NULL OR j.jobSubSubCategory = CAST(:subSubCategory AS string)) " +
            "AND (:searchTerm IS NULL OR " +
            "     LOWER(j.jobTitle) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
            "     EXISTS (SELECT 1 FROM j.keywords k WHERE LOWER(k) LIKE LOWER(CONCAT('%', :searchTerm, '%')))) " +
            "AND j.companyProfile.companyProfileId = :companyProfileId")
    Page<JobPost> findJobsByCompanyWithFilters(
            @Param("status") JobStatus status,
            @Param("location") String location,
            @Param("salary") Long salary,
            @Param("jobType") String jobType,
            @Param("careerLevel") String careerLevel,
            @Param("gender") String gender,
            @Param("industry") String industry,
            @Param("qualification") String qualification,
            @Param("jobTitle") String jobTitle,
            @Param("specialisms") String specialisms,
            @Param("experience") String experience,
            @Param("country") String country,
            @Param("city") String city,
            @Param("category") Integer category,
            @Param("subCategory") Integer subCategory,
            @Param("subSubCategory") Integer subSubCategory,
            @Param("searchTerm") String searchTerm,
            @Param("companyProfileId") Long companyProfileId,
            Pageable pageable);

    List<JobPost> findByJobOpeningDateBetweenAndStatusNot(Timestamp startDate, Timestamp endDate, JobStatus status);

    List<JobPost> findByApplicationDeadlineDateBeforeAndStatus(Timestamp date, JobStatus status);

    @Query("SELECT j FROM JobPost j LEFT JOIN FETCH j.companyProfile cp LEFT JOIN FETCH j.postedBy LEFT JOIN FETCH j.keywords WHERE j.jobId = :jobId")
    Optional<JobPost> findByIdWithRelationships(@Param("jobId") Long jobId);

    @Query("SELECT DISTINCT NEW com.job.jobportal.dto.CityStateDTO(j.city, j.state, j.district) FROM JobPost j WHERE j.city IS NOT NULL AND j.state IS NOT NULL")
    List<CityStateDTO> findDistinctCityStateCombo();

    List<JobPost> findByCompanyProfileCompanyProfileId(Long companyProfileId);

    @Query("SELECT j FROM JobPost j LEFT JOIN FETCH j.companyProfile LEFT JOIN FETCH j.postedBy LEFT JOIN FETCH j.keywords WHERE j.companyProfile.companyProfileId = :companyProfileId")
    List<JobPost> findByCompanyProfileIdWithRelationships(@Param("companyProfileId") Long companyProfileId);

    @Query("SELECT COALESCE(SUM(CASE WHEN j.numberOfPositions IS NULL OR j.numberOfPositions <= 0 THEN 1 ELSE j.numberOfPositions END), 0) FROM JobPost j WHERE j.companyProfile.companyProfileId = :companyProfileId AND j.status = 'ACTIVE'")
    Long countActiveJobsByCompanyProfileId(@Param("companyProfileId") Long companyProfileId);

    @Query("SELECT COUNT(j) FROM JobPost j WHERE j.companyProfile.companyProfileId = :companyProfileId AND j.status = 'ACTIVE'")
    Long countActiveJobPostsByCompanyProfileId(@Param("companyProfileId") Long companyProfileId);
}