package com.job.jobportal.repository;

import com.job.jobportal.model.CandidateProfile;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface CandidateProfileRepo extends JpaRepository<CandidateProfile, Long> {

    Optional<CandidateProfile> findByRegistereduser_Userid(Long userId);

    @Query("SELECT cp FROM CandidateProfile cp WHERE " +
            "(:isActive IS NULL OR cp.isActive = :isActive) AND " +
            "(:location IS NULL OR LOWER(cp.location) LIKE LOWER(CONCAT('%', :location, '%'))) AND " +
            "(:skills IS NULL OR LOWER(cp.skills) LIKE LOWER(CONCAT('%', :skills, '%'))) AND " +
            "(:yearsOfExperience IS NULL OR cp.yearsOfExperience >= :yearsOfExperience) AND " +
            "(:addressCountry IS NULL OR LOWER(cp.addressCountry) LIKE LOWER(CONCAT('%', :addressCountry, '%')))")
    Page<CandidateProfile> findByFilters(
            @Param("isActive") Boolean isActive,
            @Param("location") String location,
            @Param("skills") String skills,
            @Param("yearsOfExperience") Integer yearsOfExperience,
            @Param("addressCountry") String addressCountry,
            Pageable pageable);
}
