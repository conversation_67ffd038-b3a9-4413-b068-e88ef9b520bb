INSERT INTO account_details (is_active, is_delete_scheduled, is_premium_account)
VALUES (1, 0, 1), (1, 0, 1)$$

INSERT INTO roles (roleName)
VALUES ('ADMIN'), ('MANAGER'), ('STAFF'), ('RECRUITER'), ('CANDIDATE'), ('USER')$$

INSERT INTO registereduser (has_password, has_company_profile_id, has_candidate_profile, userid, email, password, confirmpassword, username, account_details_id, provider)
VALUES
(1, false, false, 1, '<EMAIL>', '$2a$10$KKlKOTakQ0K5ro4s4eqFpekCsR148SluKjeS4EIoXIRLtuyHVaSti', '$2a$10$KKlKOTakQ0K5ro4s4eqFpekCsR148SluKjeS4EIoXIRLtuyHVaSti', 'superadmin', 1, 'local'),
(1, false, false, 2, '<EMAIL>', '$2a$10$KKlKOTakQ0K5ro4s4eqFpekCsR148SluKjeS4EIoXIRLtuyHVaSti', '$2a$10$KKlKOTakQ0K5ro4s4eqFpekCsR148SluKjeS4EIoXIRLtuyHVaSti', 'marketing', 2, 'local')
$$

INSERT INTO users_roles (user_id, role_id)
VALUES
(1, 1),
(2, 3)$$

INSERT INTO component_type (id, name) VALUES
(1, 'skills'),
(2, 'departments'),
(3, 'locations'),
(4, 'career_level'),
(5, 'experience'),
(6, 'manage_jobs'),
(7, 'all_applications'),
(8, 'qualifications'),
(9, 'nature_of_business'),
(10, 'company_size'),
(11, 'employee_count_range'),
(12, 'job_categories'),
(13, 'job_type'),
(14, 'salary_currency'),
(15, 'pay_type'),
(16, 'application_status'),
(17, 'job_subcategories'),
(18, 'resources'),
(19, 'tools'),
(20, 'resource_subcategories'),
(21, 'seo_categories'),
(22, 'seo_subcategories'),
(23, 'job_subsubcategories'),
(24, 'districts'),
(25, 'seo_subsubcategories'),
(26, 'permission_types'),
(27, 'subscription_plan_permissions')$$


INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(1, 1, 'A&E'),
(1, 2, 'Cardiology'),
(1, 3, 'Medical'),
(1, 4, 'General wards'),
(1, 5, 'IT Consultant'),
(1, 6, 'ITU/HDU'),
(1, 7, 'Obs & Gynae'),
(1, 8, 'Surgical'),
(1, 9, 'Mental health'),
(1, 10, 'Software Engineer'),
(1, 11, 'Theatres'),
(1, 12, 'Midwifery'),
(1, 13, 'Orthopaedics'),
(1, 14, 'Community'),
(1, 15, 'Developer'),
(1, 16, 'Endoscopy'),
(1, 17, 'Paediatrics'),
(1, 18, 'ODP'),
(1, 19, 'General Practitioner'),
(1, 20, 'HR Admin'),
(1, 21, 'Chemotherapy'),
(1, 22, 'Radiology'),
(1, 23, 'Urology'),
(1, 24, 'Nurse practitioner'),
(1, 25, 'Testing'),
(1, 26, 'Neonatal/PICU'),
(1, 27, 'Palliative'),
(1, 28, 'Dialysis'),
(1, 29, 'ENT'),
(1, 30, 'Others')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(2, 1, 'HR'),
(2, 2, 'Finance'),
(2, 3, 'Engineering'),
(2, 4, 'Health Care'),
(2, 5, 'Social Care')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(3, 1, 'New York'),
(3, 2, 'London'),
(3, 3, 'Mumbai')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(4, 1, 'Entry level'),
(4, 2, 'Junior level'),
(4, 3, 'Mid-Level'),
(4, 4, 'Senior-Level'),
(4, 5, 'Executive/Management'),
(4, 6, 'Others')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(5, 1, 'No experience'),
(5, 2, 'Less than 1 year'),
(5, 3, '1-3 years'),
(5, 4, '3-5 years'),
(5, 5, '5-10 years'),
(5, 6, '10-15 years'),
(5, 7, '15+ years')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(6, 1, 'Today'),
(6, 2, 'Last 7 days'),
(6, 3, 'Last 30 days'),
(6, 4, 'Last 45 days'),
(6, 5, 'Last 60 days'),
(6, 6, 'Last 90 days'),
(6, 7, 'Last 6 months'),
(6, 8, 'Last 12 months'),
(6, 9, 'Last 24 months'),
(6, 10, 'Last 5 years'),
(6, 11, 'Last 7 years')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(7, 1, 'Today'),
(7, 2, 'Last 7 days'),
(7, 3, 'Last 30 days'),
(7, 4, 'Last 45 days'),
(7, 5, 'Last 60 days'),
(7, 6, 'Last 90 days'),
(7, 7, 'Last 6 months'),
(7, 8, 'Last 12 months'),
(7, 9, 'Last 24 months'),
(7, 10, 'Last 5 years'),
(7, 11, 'Last 7 years')$$


INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(8, 1, 'GCSE/A level'),
(8, 2, 'Diploma'),
(8, 3, 'Degree'),
(8, 4, 'Post graduate'),
(8, 5, 'Doctorate'),
(8, 6, 'Certificate course'),
(8, 7, 'Others')$$


INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(9, 1, 'Accounting & Finance'),
(9, 2, 'Education'),
(9, 3, 'Engineering'),
(9, 4, 'Health care'),
(9, 5, 'Information technology'),
(9, 6, 'Social care')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(10, 1, '1-50'),
(10, 2, '101 - 200'),
(10, 3, '201 - 300'),
(10, 4, '301 - 400'),
(10, 5, '401 - 500'),
(10, 6, '501 - 750'),
(10, 7, '51 - 100'),
(10, 8, '751 - 1000'),
(10, 9, '1000+')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(11, 1, '1-10'),
(11, 2, '11-50'),
(11, 3, '51-100'),
(11, 4, '101-250'),
(11, 5, '251-500'),
(11, 6, '501-750'),
(11, 7, '751-1000'),
(11, 8, '1000+')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(12, 1, 'Allied health professionals'),
(12, 2, 'Ambulance service team'),
(12, 3, 'Dental team'),
(12, 4, 'Doctors'),
(12, 5, 'Estates and facilities'),
(12, 6, 'Health informatics'),
(12, 7, 'Healthcare science'),
(12, 8, 'Healthcare support worker'),
(12, 9, 'Management'),
(12, 10, 'Medical associate professions'),
(12, 11, 'Midwifery'),
(12, 12, 'Nursing'),
(12, 13, 'Pharmacy'),
(12, 14, 'Psychological professions'),
(12, 15, 'Public health'),
(12, 16, 'Wider healthcare team')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(13, 1, 'Full-time'),
(13, 2, 'Part-time'),
(13, 3, 'Permanent'),
(13, 4, 'Contract'),
(13, 5, 'Temporary'),
(13, 6, 'Training'),
(13, 7, 'Freelancer'),
(13, 8, 'Volunteer')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(14, 1, 'GBP'),
(14, 2, 'USD'),
(14, 3, 'EUR'),
(14, 4, 'AUD'),
(14, 5, 'CAD'),
(14, 6, 'INR')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(15, 1, 'Hourly'),
(15, 2, 'Weekly'),
(15, 3, 'Monthly'),
(15, 4, 'Yearly')$$


INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(16, 1, 'Applied'),
(16, 2, 'Shortlisted'),
(16, 3, 'Interview'),
(16, 4, 'Hired'),
(16, 5, 'Rejected')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(17, 1, 'Art therapist/art psychotherapist|1'),
(17, 2, 'Diagnostic radiographer|1'),
(17, 3, 'Dietitian|1'),
(17, 4, 'Dramatherapist|1'),
(17, 5, 'Music therapist|1'),
(17, 6, 'Occupational therapist|1'),
(17, 7, 'Operating department practitioner|1'),
(17, 8, 'Orthoptist|1'),
(17, 9, 'Osteopath|1'),
(17, 10, 'Paramedic|1'),
(17, 11, 'Physiotherapist|1'),
(17, 12, 'Podiatrist|1'),
(17, 13, 'Prosthetist/orthotist|1'),
(17, 14, 'Speech and language therapist|1'),
(17, 15, 'Therapeutic radiographer|1')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(17, 16, 'Ambulance care assistant and Patient Transport Service (PTS) driver|2'),
(17, 17, 'Call handler/emergency medical dispatcher|2'),
(17, 18, 'Emergency care assistant|2'),
(17, 19, 'Emergency medical technician|2'),
(17, 20, 'Experienced paramedic|2'),
(17, 21, 'Patient transport service (PTS) call handler|2')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(17, 22, 'Dental|3'),
(17, 23, 'Dental nurse|3'),
(17, 24, 'Dental technician/ dental technologist|3'),
(17, 25, 'Dental Therapist|3'),
(17, 26, 'Dentist|3')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(17, 27, 'Anesthesia|4'),
(17, 28, 'Clinical oncology|4'),
(17, 29, 'Clinical radiology|4'),
(17, 30, 'Community sexual and reproductive health|4'),
(17, 31, 'Emergency medicine|4'),
(17, 32, 'General practitioner|4'),
(17, 33, 'Intensive care medicine|4'),
(17, 34, 'Obstetrics and gynaecology|4'),
(17, 35, 'Occupational medicine|4'),
(17, 36, 'Ophthalmology|4'),
(17, 37, 'Medicine Doctor|4'),
(17, 38, 'Pathology Doctor|4'),
(17, 39, 'Psychiatry Doctor|4'),
(17, 40, 'Surgery Doctor|4'),
(17, 41, 'Paediatrics|4')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(17, 42, 'Domestic services|5'),
(17, 43, 'Estates services|5'),
(17, 44, 'Support services|5')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(17, 45, 'Clinical informatics|6'),
(17, 46, 'Education and training roles|6'),
(17, 47, 'Health records and patient administration|6'),
(17, 48, 'Information and communication technology|6'),
(17, 49, 'Information management staff|6'),
(17, 50, 'Knowledge and library services|6'),
(17, 51, 'Project and programme management|6')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(17, 52, 'Clinical bioinformatics|7'),
(17, 53, 'Life sciences|7'),
(17, 54, 'Physical sciences and biomedical engineering|7'),
(17, 55, 'Physiological sciences|7')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(17, 56, 'Dietetic assistant|8'),
(17, 57, 'Healthcare assistant|8'),
(17, 58, 'Healthcare support worker|8'),
(17, 59, 'Mammography associate|8'),
(17, 60, 'Maternity support worker|8'),
(17, 61, 'Occupational therapy support worker|8'),
(17, 62, 'Podiatry assistant|8'),
(17, 63, 'Prosthetic technician|8'),
(17, 64, 'Radiography assistants and imaging support workers|8'),
(17, 65, 'Speech and language therapy assistant|8')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(17, 66, 'Clinical manager|9'),
(17, 67, 'Estates manager|9'),
(17, 68, 'Finance manager|9'),
(17, 69, 'General management|9'),
(17, 70, 'Human resources (HR) manager|9'),
(17, 71, 'Operational management|9'),
(17, 72, 'Practice manager|9'),
(17, 73, 'Project manager|9')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(17, 74, 'Anaesthesia associate|10'),
(17, 75, 'Physician associate|10'),
(17, 76, 'Surgical care practitioner|10')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(17, 77, 'Midwife|11')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(17, 78, 'Adult nurse|12'),
(17, 79, 'Childrens nurse|12'),
(17, 80, 'District nurse|12'),
(17, 81, 'General practice nurse|12'),
(17, 82, 'Learning disability nurse|12'),
(17, 83, 'Mental health nurse|12'),
(17, 84, 'Nursing associate|12'),
(17, 85, 'Prison nurse|12'),
(17, 86, 'Theatre nurse|12')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(17, 87, 'Pharmacist|13'),
(17, 88, 'Pharmacy assistant|13'),
(17, 89, 'Pharmacy technician|13')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(17, 90, 'Adult psychotherapist|14'),
(17, 91, 'Assistant psychologist|14'),
(17, 92, 'CBT therapist|14'),
(17, 93, 'Child and adolescent psychotherapist|14'),
(17, 94, 'Childrens wellbeing practitioner|14'),
(17, 95, 'Clinical associate in psychology|14'),
(17, 96, 'Clinical psychologist|14'),
(17, 97, 'Counselling psychologist|14'),
(17, 98, 'Counsellor|14'),
(17, 99, 'Education mental health practitioner|14'),
(17, 100, 'Family and systemic psychotherapist|14'),
(17, 101, 'Forensic psychologist|14'),
(17, 102, 'Health psychologist|14'),
(17, 103, 'High intensity therapist|14'),
(17, 104, 'Mental health and wellbeing practitioner|14'),
(17, 105, 'Peer support worker|14'),
(17, 106, 'Psychological wellbeing practitioner|14'),
(17, 107, 'Youth intensive psychological practitioner|14')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(17, 108, 'Director of public health|15'),
(17, 109, 'Environmental health professional|15'),
(17, 110, 'Health trainer|15'),
(17, 111, 'Health visitor|15'),
(17, 112, 'Occupational health nurse|15'),
(17, 113, 'Public health academic|15'),
(17, 114, 'Public health consultants and specialists|15'),
(17, 115, 'Public health knowledge and intelligence professional|15'),
(17, 116, 'Public health manager|15'),
(17, 117, 'Public health nurse|15'),
(17, 118, 'Public health practitioner|15'),
(17, 119, 'School nurse|15')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(17, 120, 'Wider healthcare team|16'),
(17, 121, 'Clinical support staff|16'),
(17, 122, 'Corporate services|16')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(20, 1, 'CV Writing Tips|11'),
(20, 2, 'Interview Preparation|11'),
(20, 3, 'Career Development|11'),
(20, 4, 'Job Market Trends|11'),
(20, 5, 'Healthcare Industry News|11')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(20, 6, 'CV Templates|12'),
(20, 7, 'CV Help & Tips|12'),
(20, 8, 'Personal Statement|12'),
(20, 9, 'Interview Questions|12'),
(20, 10, 'Interview Advice|12'),
(20, 11, 'Career Development|12'),
(20, 12, 'Job Applications|12'),
(20, 13, 'Career Change|12'),
(20, 14, 'Job Descriptions|12'),
(20, 15, 'Starting a New Job|12')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(20, 16, 'Nursing Roles|13'),
(20, 17, 'Doctor Roles|13'),
(20, 18, 'Allied Health Roles|13'),
(20, 19, 'Administrative Roles|13'),
(20, 20, 'Support Staff Roles|13')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(20, 21, 'Hiring Tips|14'),
(20, 22, 'Job Posting Guidelines|14'),
(20, 23, 'Candidate Screening|14')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(21, 1, 'Blog'),
(21, 2, 'Career Guide'),
(21, 3, 'Explore Roles'),
(21, 4, 'Employer Resource')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(22, 1, 'Tips|1'),
(22, 2, 'News|1'),
(22, 3, 'Tutorials|1'),
(22, 4, 'Case Studies|1')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(22, 5, 'Resume|2'),
(22, 6, 'Interview|2'),
(22, 7, 'Job Search|2'),
(22, 8, 'Career Development|2')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(22, 9, 'Healthcare|3'),
(22, 10, 'Technology|3'),
(22, 11, 'Finance|3'),
(22, 12, 'Education|3')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(22, 13, 'Hiring|4'),
(22, 14, 'Retention|4'),
(22, 15, 'Workplace|4'),
(22, 16, 'Compliance|4')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(20, 24, 'Employer Branding|14'),
(20, 25, 'Retention Strategies|14')$$

-- add job sub-subcategories
INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(23, 1, 'Acute internal medicine|37'),
(23, 2, 'Allergy|37'),
(23, 3, 'Audiovestibular medicine|37'),
(23, 4, 'Cardiologist|37'),
(23, 5, 'Clinical genetics|37'),
(23, 6, 'Clinical neurophysiology|37'),
(23, 7, 'Clinical pharmacology and therapeutics|37'),
(23, 8, 'Dermatology|37'),
(23, 9, 'Endocrinology and diabetes|37'),
(23, 10, 'Gastroenterology|37'),
(23, 11, 'General internal medicine|37'),
(23, 12, 'Genitourinary medicine|37'),
(23, 13, 'Geriatric medicine|37'),
(23, 14, 'Immunology|37'),
(23, 15, 'Infectious diseases|37'),
(23, 16, 'Medical oncology|37'),
(23, 17, 'Medical ophthalmology|37'),
(23, 18, 'Metabolic Medicine|37'),
(23, 19, 'Neurologist|37'),
(23, 20, 'Nuclear medicine|37'),
(23, 21, 'Palliative medicine|37'),
(23, 22, 'Pharmaceutical medicine|37'),
(23, 23, 'Rehabilitation medicine|37'),
(23, 24, 'Renal medicine|37'),
(23, 25, 'Respiratory medicine|37'),
(23, 26, 'Rheumatology|37'),
(23, 27, 'Sport and exercise medicine|37'),
(23, 28, 'Stroke medicine|37'),
(23, 29, 'Tropical medicine|37')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(23, 30, 'Chemical pathology|38'),
(23, 31, 'Haematology (doctor)|38'),
(23, 32, 'Histopathology (doctor)|38'),
(23, 33, 'Medical microbiology and virology (doctor)|38')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(23, 34, 'Child and adolescent psychiatry|39'),
(23, 35, 'Forensic psychiatry|39'),
(23, 36, 'General psychiatry|39'),
(23, 37, 'Liaison psychiatry|39'),
(23, 38, 'Medical psychotherapy|39'),
(23, 39, 'Old age psychiatry|39'),
(23, 40, 'Psychiatry of intellectual disability (PID)|39')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(23, 41, 'Cardiothoracic surgeon|40'),
(23, 42, 'General surgery|40'),
(23, 43, 'Neurosurgeon|40'),
(23, 44, 'Oral and maxillofacial surgery|40'),
(23, 45, 'Otorhinolaryngology (ear, nose and throat (ENT) surgery)|40'),
(23, 46, 'Paediatric surgery|40'),
(23, 47, 'Plastic surgery|40'),
(23, 48, 'Trauma and orthopaedic surgery|40'),
(23, 49, 'Urology|40'),
(23, 50, 'Vascular surgery|40')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(23, 51, 'Paediatric cardiology|41'),
(23, 52, 'Paediatrician|41')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(23, 53, 'Communications and corporate affairs|69'),
(23, 54, 'Performance and quality management|69'),
(23, 55, 'Purchasing and contract management|69'),
(23, 56, 'Strategic management|69')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(23, 57, 'Administrative management|71'),
(23, 58, 'Decontamination services management|71'),
(23, 59, 'Facilities management|71'),
(23, 60, 'Hotel services management|71'),
(23, 61, 'Integrated urgent care/NHS 111 team leader|71')$$

-- Add districts
INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(24, 1, 'Chennai'),
(24, 2, 'Bangalore'),
(24, 3, 'Hyderabad'),
(24, 4, 'Mumbai')$$

-- Add SEO subsubcategories with proper parent-child relationships
INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(25, 1, 'Resume Writing Tips|5'),
(25, 2, 'Cover Letter Examples|5'),
(25, 3, 'CV Templates|5'),
(25, 4, 'Personal Statement Guide|5'),
(25, 5, 'Job Application Tips|5'),
(25, 6, 'Interview Preparation|6'),
(25, 7, 'Common Interview Questions|6'),
(25, 8, 'Interview Techniques|6'),
(25, 9, 'Interview Attire|6'),
(25, 10, 'Interview Follow-up|6')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(25, 11, 'Job Search Strategies|7'),
(25, 12, 'Online Job Boards|7'),
(25, 13, 'Networking Tips|7'),
(25, 14, 'Social Media Job Search|7'),
(25, 15, 'Job Fairs|7'),
(25, 16, 'Career Planning|8'),
(25, 17, 'Career Change Advice|8'),
(25, 18, 'Professional Development|8'),
(25, 19, 'Continuing Education|8'),
(25, 20, 'Promotion Strategies|8')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(25, 21, 'Healthcare Careers|9'),
(25, 22, 'Medical Professions|9'),
(25, 23, 'Nursing Careers|9'),
(25, 24, 'Allied Health Professions|9'),
(25, 25, 'Healthcare Administration|9'),
(25, 26, 'Software Development|10'),
(25, 27, 'Data Science|10'),
(25, 28, 'Cybersecurity|10'),
(25, 29, 'IT Support|10'),
(25, 30, 'Web Development|10')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(25, 31, 'Banking Careers|11'),
(25, 32, 'Investment Banking|11'),
(25, 33, 'Financial Analysis|11'),
(25, 34, 'Accounting Careers|11'),
(25, 35, 'Insurance Industry|11'),
(25, 36, 'Teaching Careers|12'),
(25, 37, 'Education Administration|12'),
(25, 38, 'Higher Education|12'),
(25, 39, 'Special Education|12'),
(25, 40, 'Early Childhood Education|12')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(25, 41, 'Recruitment Best Practices|13'),
(25, 42, 'Talent Acquisition|13'),
(25, 43, 'Interviewing Techniques|13'),
(25, 44, 'Candidate Screening|13'),
(25, 45, 'Job Description Writing|13'),
(25, 46, 'Employee Retention|14'),
(25, 47, 'Staff Development|14'),
(25, 48, 'Employee Benefits|14'),
(25, 49, 'Recognition Programs|14'),
(25, 50, 'Reducing Turnover|14')$$

INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(25, 51, 'Remote Work Policies|15'),
(25, 52, 'Office Environment|15'),
(25, 53, 'Team Building|15'),
(25, 54, 'Workplace Culture|15'),
(25, 55, 'Work-Life Balance|15'),
(25, 56, 'Employment Law|16'),
(25, 57, 'HR Compliance|16'),
(25, 58, 'Workplace Safety|16'),
(25, 59, 'Data Protection|16'),
(25, 60, 'Equal Opportunity|16')$$

-- Add job categories data to SEO categories
INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(21, 5, 'Allied health professionals'),
(21, 6, 'Ambulance service team'),
(21, 7, 'Dental team'),
(21, 8, 'Doctors'),
(21, 9, 'Estates and facilities'),
(21, 10, 'Health informatics'),
(21, 11, 'Healthcare science'),
(21, 12, 'Healthcare support worker'),
(21, 13, 'Management'),
(21, 14, 'Medical associate professions'),
(21, 15, 'Midwifery'),
(21, 16, 'Nursing'),
(21, 17, 'Pharmacy'),
(21, 18, 'Psychological professions'),
(21, 19, 'Public health'),
(21, 20, 'Wider healthcare team')$$

-- Add job subcategories data to SEO subcategories
INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(22, 17, 'Art therapist/art psychotherapist|5'),
(22, 18, 'Diagnostic radiographer|5'),
(22, 19, 'Dietitian|5'),
(22, 20, 'Dramatherapist|5'),
(22, 21, 'Music therapist|5'),
(22, 22, 'Occupational therapist|5'),
(22, 23, 'Operating department practitioner|5'),
(22, 24, 'Orthoptist|5'),
(22, 25, 'Osteopath|5'),
(22, 26, 'Paramedic|5'),
(22, 27, 'Physiotherapist|5'),
(22, 28, 'Podiatrist|5'),
(22, 29, 'Prosthetist/orthotist|5'),
(22, 30, 'Speech and language therapist|5'),
(22, 31, 'Therapeutic radiographer|5'),
(22, 32, 'Ambulance care assistant and Patient Transport Service (PTS) driver|6'),
(22, 33, 'Call handler/emergency medical dispatcher|6'),
(22, 34, 'Emergency care assistant|6'),
(22, 35, 'Emergency medical technician|6'),
(22, 36, 'Experienced paramedic|6'),
(22, 37, 'Patient transport service (PTS) call handler|6'),
(22, 38, 'Dental|7'),
(22, 39, 'Dental nurse|7'),
(22, 40, 'Dental technician/ dental technologist|7'),
(22, 41, 'Dental Therapist|7'),
(22, 42, 'Dentist|7'),
(22, 43, 'Anesthesia|8'),
(22, 44, 'Clinical oncology|8'),
(22, 45, 'Clinical radiology|8'),
(22, 46, 'Community sexual and reproductive health|8'),
(22, 47, 'Emergency medicine|8'),
(22, 48, 'General practitioner|8'),
(22, 49, 'Intensive care medicine|8'),
(22, 50, 'Obstetrics and gynaecology|8'),
(22, 51, 'Occupational medicine|8'),
(22, 52, 'Ophthalmology|8'),
(22, 53, 'Medicine Doctor|8'),
(22, 54, 'Pathology Doctor|8'),
(22, 55, 'Psychiatry Doctor|8'),
(22, 56, 'Surgery Doctor|8'),
(22, 57, 'Paediatrics|8')$$

-- Add SEO subcategories for healthcare-related categories
INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
-- Dental team subcategories
(22, 58, 'Dental News|7'),
(22, 59, 'Dental Careers|7'),
(22, 60, 'Dental Education|7'),
(22, 61, 'Dental Technology|7'),

-- Doctors subcategories
(22, 62, 'Medical News|8'),
(22, 63, 'Medical Research|8'),
(22, 64, 'Clinical Practice|8'),
(22, 65, 'Medical Education|8'),

-- Nursing subcategories
(22, 66, 'Nursing News|16'),
(22, 67, 'Nursing Careers|16'),
(22, 68, 'Nursing Education|16'),
(22, 69, 'Clinical Skills|16')$$

-- Add SEO subsubcategories with correct parent IDs referencing SEO subcategories
INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
-- Medical topics under Medical News (subcategory_id 62)
(25, 1, 'Acute internal medicine|62'),
(25, 2, 'Allergy|62'),
(25, 3, 'Audiovestibular medicine|62'),
(25, 4, 'Cardiologist|62'),
(25, 5, 'Clinical genetics|62'),
(25, 6, 'Clinical neurophysiology|62'),
(25, 7, 'Clinical pharmacology and therapeutics|62'),
(25, 8, 'Dermatology|62'),
(25, 9, 'Endocrinology and diabetes|62'),
(25, 10, 'Gastroenterology|62'),
(25, 11, 'General internal medicine|62'),
(25, 12, 'Genitourinary medicine|62'),
(25, 13, 'Geriatric medicine|62'),
(25, 14, 'Immunology|62'),
(25, 15, 'Infectious diseases|62'),

-- Medical specialties under Clinical Practice (subcategory_id 64)
(25, 16, 'Medical oncology|64'),
(25, 17, 'Medical ophthalmology|64'),
(25, 18, 'Metabolic Medicine|64'),
(25, 19, 'Neurologist|64'),
(25, 20, 'Nuclear medicine|64'),
(25, 21, 'Palliative medicine|64'),
(25, 22, 'Pharmaceutical medicine|64'),
(25, 23, 'Rehabilitation medicine|64'),
(25, 24, 'Renal medicine|64'),
(25, 25, 'Respiratory medicine|64'),

-- Surgical specialties under Dental News (subcategory_id 58)
(25, 26, 'Dental Surgery|58'),
(25, 27, 'Orthodontics|58'),
(25, 28, 'Periodontics|58'),
(25, 29, 'Endodontics|58'),
(25, 30, 'Prosthodontics|58'),
(25, 31, 'Pediatric Dentistry|58'),
(25, 32, 'Oral Medicine|58'),
(25, 33, 'Dental Public Health|58'),

-- Psychiatric specialties under Medical Research (subcategory_id 63)
(25, 34, 'Child and adolescent psychiatry|63'),
(25, 35, 'Forensic psychiatry|63'),
(25, 36, 'General psychiatry|63'),
(25, 37, 'Liaison psychiatry|63'),
(25, 38, 'Medical psychotherapy|63'),
(25, 39, 'Old age psychiatry|63'),
(25, 40, 'Psychiatry of intellectual disability|63'),

-- Surgical specialties under Medical Education (subcategory_id 65)
(25, 41, 'Cardiothoracic surgery|65'),
(25, 42, 'General surgery|65'),
(25, 43, 'Neurosurgery|65'),
(25, 44, 'Oral and maxillofacial surgery|65'),
(25, 45, 'Otorhinolaryngology|65'),
(25, 46, 'Paediatric surgery|65'),
(25, 47, 'Plastic surgery|65'),
(25, 48, 'Trauma and orthopaedic surgery|65'),
(25, 49, 'Urology|65'),
(25, 50, 'Vascular surgery|65')$$

-- Add master data for permission types
INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(26, 0, 'VIEW_APPLICANTS'),
(26, 1, 'CONTACT_APPLICANTS'),
(26, 2, 'POST_JOBS'),
(26, 3, 'FEATURED_JOBS'),
(26, 4, 'ANALYTICS'),
(26, 5, 'BULK_ACTIONS')$$

-- Add master data for subscription plan permissions
INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(27, 0, '000000'), -- Free Plan - No permissions
(27, 1, '000000'), -- Basic Plan - Not used, no permissions
(27, 2, '101110'), -- Standard Plan ("starter") - VIEW_APPLICANTS, POST_JOBS, FEATURED_JOBS, BULK_ACTIONS
(27, 3, '111110'), -- Premium Plan ("advance") - VIEW_APPLICANTS, CONTACT_APPLICANTS, POST_JOBS, FEATURED_JOBS, ANALYTICS
(27, 4, '111111'), -- Enterprise Plan - All permissions
(27, 5, '101110')  -- Trial Plan - Same as Standard Plan
$$

-- Add subscription plan data with permissions (without plan_object as it will store Stripe price IDs)
-- Plan IDs match the constants in ConstantsUtil.java:
-- SUBSCRIPTION_STANDARD_PLAN = 2
-- SUBSCRIPTION_PREMIUM_PLAN = 3
-- SUBSCRIPTION_ENTERPRISE_PLAN = 4
-- SUBSCRIPTION_TRIAL_PLAN = 5
INSERT INTO subscription_plan (plan_id, plan_name, permissions) VALUES
(5, 'Trial', '101110'),
(2, 'Standard Monthly', '101110'),
(20, 'Standard Yearly', '101110'),
(3, 'Premium Monthly', '111110'),
(30, 'Premium Yearly', '111110'),
(4, 'Enterprise Monthly', '111111'),
(40, 'Enterprise Yearly', '111111')
$$

-- Add subcategories for Estates and facilities (category_id 5)
INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(17, 123, 'Catering manager|5'),
(17, 124, 'Chef/cook|5'),
(17, 125, 'Domestic services staff|5'),
(17, 126, 'Housekeeper|5'),
(17, 127, 'Linen services staff|5'),
(17, 128, 'Bricklayer|5'),
(17, 129, 'Caretaker|5'),
(17, 130, 'Carpenter/joiner|5'),
(17, 131, 'Electrician|5'),
(17, 132, 'Engineer|5'),
(17, 133, 'Estates technician|5'),
(17, 134, 'Gardeners and grounds staff|5'),
(17, 135, 'Painter and decorator|5'),
(17, 136, 'Plumber|5'),
(17, 137, 'Surveyor|5'),
(17, 138, 'Tiler|5'),
(17, 139, 'Window cleaner|5'),
(17, 140, 'Driver|5'),
(17, 141, 'Fire safety officer|5'),
(17, 142, 'Health and safety officer|5'),
(17, 143, 'Porter|5'),
(17, 144, 'Security staff|5'),
(17, 145, 'Stores and distribution staff|5')$$

-- Add subcategories for Health informatics (category_id 6)
INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(17, 146, 'Clinical bioinformatics|6')$$

-- Add subcategories for Healthcare science (category_id 7)
INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(17, 147, 'Clinical bioinformatics (genomics)|7'),
(17, 148, 'Clinical bioinformatics (health informatics)|7'),
(17, 149, 'Clinical bioinformatics (physical sciences)|7')$$

-- Add subcategories for Wider healthcare team (category_id 16)
INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(17, 150, 'Clerk|16'),
(17, 151, 'Health records staff|16'),
(17, 152, 'Medical secretary/personal assistant|16'),
(17, 153, 'Receptionist|16'),
(17, 154, 'Secretary/typist|16'),
(17, 155, 'Telephonist/switchboard operator|16'),
(17, 156, 'Assistant practitioner|16'),
(17, 157, 'Cardiographer|16'),
(17, 158, 'Creative therapy support roles|16'),
(17, 159, 'Dental support worker|16'),
(17, 160, 'Donor carer|16'),
(17, 161, 'Employment specialist|16'),
(17, 162, 'Health play staff|16'),
(17, 163, 'Healthcare science assistants and associates|16'),
(17, 164, 'Integrated urgent care/NHS 111 roles|16'),
(17, 165, 'Mammographer|16'),
(17, 166, 'Medical support worker|16'),
(17, 167, 'Newborn hearing screener|16'),
(17, 168, 'Nutritionist|16'),
(17, 169, 'Optometrist|16'),
(17, 170, 'Orthopaedic practitioner|16'),
(17, 171, 'Phlebotomist|16'),
(17, 172, 'Social prescribing link worker|16'),
(17, 173, 'Social worker|16'),
(17, 174, 'Support, time and recovery worker|16'),
(17, 175, 'Theatre support worker|16'),
(17, 176, 'Arts manager/arts co-ordinator|16'),
(17, 177, 'Chaplain|16'),
(17, 178, 'Communications/public relations staff|16'),
(17, 179, 'Finance staff|16'),
(17, 180, 'Human resources staff|16'),
(17, 181, 'Nursery nurse and nursery assistant|16')$$

-- Add sub-subcategories for Domestic services (subcategory_id 42)
INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(23, 62, 'Catering manager|42'),
(23, 63, 'Chef/cook|42'),
(23, 64, 'Domestic services staff|42'),
(23, 65, 'Housekeeper|42'),
(23, 66, 'Linen services staff|42')$$

-- Add sub-subcategories for Estates services (subcategory_id 43)
INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(23, 67, 'Bricklayer|43'),
(23, 68, 'Caretaker|43'),
(23, 69, 'Carpenter/joiner|43'),
(23, 70, 'Electrician|43'),
(23, 71, 'Engineer|43'),
(23, 72, 'Estates technician|43'),
(23, 73, 'Gardeners and grounds staff|43'),
(23, 74, 'Painter and decorator|43'),
(23, 75, 'Plumber|43'),
(23, 76, 'Surveyor|43'),
(23, 77, 'Tiler|43'),
(23, 78, 'Window cleaner|43')$$

-- Add sub-subcategories for Support services (subcategory_id 44)
INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(23, 79, 'Driver|44'),
(23, 80, 'Fire safety officer|44'),
(23, 81, 'Health and safety officer|44'),
(23, 82, 'Porter|44'),
(23, 83, 'Security staff|44'),
(23, 84, 'Stores and distribution staff|44')$$

-- Add sub-subcategories for Clinical bioinformatics (subcategory_id 52)
INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(23, 85, 'Clinical bioinformatics (genomics)|52'),
(23, 86, 'Clinical bioinformatics (health informatics)|52'),
(23, 87, 'Clinical bioinformatics (physical sciences)|52')$$

-- Add sub-subcategories for Life sciences (subcategory_id 53)
INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(23, 88, 'Analytical toxicology|53'),
(23, 89, 'Anatomical pathology|53'),
(23, 90, 'Biomedical science|53'),
(23, 91, 'Cancer genomics|53'),
(23, 92, 'Clinical biochemistry|53'),
(23, 93, 'Clinical immunology|53'),
(23, 94, 'Cytopathology|53'),
(23, 95, 'Genomic counselling|53'),
(23, 96, 'Genomics|53'),
(23, 97, 'Haematology (healthcare scientist)|53'),
(23, 98, 'Infection sciences|53'),
(23, 99, 'Microbiology (healthcare scientist)|53'),
(23, 100, 'Reproductive science and andrology|53'),
(23, 101, 'Virology (healthcare scientist)|53')$$

-- Add sub-subcategories for Physical sciences and biomedical engineering (subcategory_id 54)
INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(23, 102, 'Clinical measurement|54'),
(23, 103, 'Clinical or medical technology in medical physics|54'),
(23, 104, 'Clinical pharmaceutical science|54'),
(23, 105, 'Clinical photography|54'),
(23, 106, 'Decontamination science (sterile services and flexible endoscopy)|54'),
(23, 107, 'Imaging (ionising)|54'),
(23, 108, 'Imaging (non-ionising)|54'),
(23, 109, 'Medical device risk management and governance|54'),
(23, 110, 'Medical engineering|54'),
(23, 111, 'Nuclear medicine (healthcare scientist)|54'),
(23, 112, 'Radiation physics and radiation safety physics|54'),
(23, 113, 'Radiotherapy physics|54'),
(23, 114, 'Reconstructive science|54'),
(23, 115, 'Rehabilitation engineering|54'),
(23, 116, 'Renal technology|54')$$

-- Add sub-subcategories for Physiological sciences (subcategory_id 55)
INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(23, 117, 'Audiology|55'),
(23, 118, 'Cardiac sciences|55'),
(23, 119, 'Clinical exercise physiologist|55'),
(23, 120, 'Clinical perfusion science|55'),
(23, 121, 'Critical care science|55'),
(23, 122, 'Gastrointestinal physiology|55'),
(23, 123, 'Hearing aid dispenser|55'),
(23, 124, 'Neurophysiology|55'),
(23, 125, 'Ophthalmic and vision science|55'),
(23, 126, 'Respiratory physiology and sleep sciences|55'),
(23, 127, 'Urodynamic science|55'),
(23, 128, 'Vascular science|55')$$

-- Add sub-subcategories for Wider healthcare team (subcategory_id 120)
INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(23, 129, 'Clerk|120'),
(23, 130, 'Health records staff|120'),
(23, 131, 'Medical secretary/personal assistant|120'),
(23, 132, 'Receptionist|120'),
(23, 133, 'Secretary/typist|120'),
(23, 134, 'Telephonist/switchboard operator|120')$$

-- Add sub-subcategories for Clinical support staff (subcategory_id 121)
INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(23, 135, 'Assistant practitioner|121'),
(23, 136, 'Cardiographer|121'),
(23, 137, 'Creative therapy support roles|121'),
(23, 138, 'Dental support worker|121'),
(23, 139, 'Donor carer|121'),
(23, 140, 'Employment specialist|121'),
(23, 141, 'Health play staff|121'),
(23, 142, 'Healthcare science assistants and associates|121'),
(23, 143, 'Integrated urgent care/NHS 111 roles|121'),
(23, 144, 'Mammographer|121'),
(23, 145, 'Medical support worker|121'),
(23, 146, 'Newborn hearing screener|121'),
(23, 147, 'Nutritionist|121'),
(23, 148, 'Optometrist|121'),
(23, 149, 'Orthopaedic practitioner|121'),
(23, 150, 'Phlebotomist|121'),
(23, 151, 'Social prescribing link worker|121'),
(23, 152, 'Social worker|121'),
(23, 153, 'Support, time and recovery worker|121'),
(23, 154, 'Theatre support worker|121')$$

-- Add sub-subcategories for Corporate services (subcategory_id 122)
INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
(23, 155, 'Arts manager/arts co-ordinator|122'),
(23, 156, 'Chaplain|122'),
(23, 157, 'Communications/public relations staff|122'),
(23, 158, 'Finance staff|122'),
(23, 159, 'Human resources staff|122'),
(23, 160, 'Nursery nurse and nursery assistant|122')$$

