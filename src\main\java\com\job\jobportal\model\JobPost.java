package com.job.jobportal.model;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;

@Entity
@Getter
@Setter
public class JobPost {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long jobId;

    private String jobTitle;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "json")
    private JsonNode jobDescription;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "json")
    private JsonNode responsibilitiesAndBenefits;

    private String departmentName;
    private Long numberOfPositions;
    private String location;
    private String jobCategory;
    private String jobSubCategory;
    private String jobSubSubCategory;
    private String district;
    private String contactEmail;
    private Timestamp postedDate;
    private Timestamp updatedDate;
    private Timestamp jobOpeningDate;
    private String username;
    private String specialisms;
    private String retail;
    private String question;
    private String jobType;
    private String careerLevel;
    private String experience;
    private String gender;
    private String industry;
    private String qualification;
    private Timestamp applicationDeadlineDate;
    private String country;
    private String city;
    private String completeAddress;
    private Double jobAddressMapLocationLattitude;
    private Double jobAddressMapLocationLongtitude;
    private String salaryCurrency;
    private String payType;
    private Long minSalary;
    private Long maxSalary;
    private Boolean hideCompensation = false;  // Optional: if you want to allow hiding salary details
    private String state;
    private String pincode;
    private Double candidateAddressMapLocationLattitude;
    private Double candidateAddressMapLocationLongtitude;

    @ElementCollection
    @CollectionTable(name = "job_keywords", joinColumns = @JoinColumn(name = "job_id"))
    @Column(name = "keyword")
    private List<String> keywords;

    @Enumerated(EnumType.STRING)
    private JobStatus status;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "companyProfileId")
    private CompanyProfile companyProfile;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "userId")
    private Registereduser postedBy;

    @Column(columnDefinition = "LONGTEXT")
    private String featureImageUrl;

    private String featureImageKey;

    @Column(columnDefinition = "LONGTEXT")
    private String videoUrl;

    private String videoKey;
}
