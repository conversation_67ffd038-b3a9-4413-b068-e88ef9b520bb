package com.job.jobportal.dto;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;

@Data
public class ProgrammaticPageDTO {

    private Long programmaticPageId;

    private String programmaticPageTitle;

    private String programmaticPageUrl;

    private String programmaticPageMetaTitle;

    private String programmaticPageMetaDescription;

    private Integer programmaticPageCategoryId;
    private String programmaticPageCategoryName;

    private Integer programmaticPageSubcategoryId;
    private String programmaticPageSubcategoryName;

    private Integer programmaticPageSubSubcategoryId;
    private String programmaticPageSubSubcategoryName;

    private Integer programmaticPageCityId;
    private String programmaticPageCity;

    private Integer programmaticPageDistrictId;
    private String programmaticPageDistrict;

    private JsonNode programmaticPageContent;

    private Timestamp programmaticPageCreatedDate;

    private Timestamp programmaticPageUpdatedDate;

    private Boolean programmaticPageIsActive;
}
