package com.job.jobportal.dto;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;

@Getter
@Setter
public class CompanyProfileDTO {

    private String authorizedPersonFirstName;
    private String authorizedPersonLastName;
    private String authorizedPersonDesignation;
    private String companyRegistrationNumber;
    private boolean isAuthorizedPerson;

    private Date companyEstYear;

    private Long companyProfileId;

    private String companyName;

    private String companyLogoURL;

    private String companyCoverURL;

    private String companyLogoKey;

    private String currentUserEmail;

    private String companyCoverKey;

    private String featureImageUrl;

    private String featureImageKey;

    private String videoUrl;

    private String videoKey;

    private String companyEmail;

    private String companyWebsite;

    private int companyTeamSize;

    private JsonNode companyDescription;

    private String companyPhoneNumber;

    private int companyAllowInSearch;

    private int companyNatureOfBusiness;

    private String companySocialLinkedIn;

    private String companySocialFacebook;

    private String companySocialTwitter;

    private String companySocialGlassDoor;

    private String companyAddressCountry;

    private String companyAddressCity;

    private String companyAddressDistrict;

    private String companyAddressLineOne;

    private String companyAddressLineTwo;

    private String companyAddressPincode;

    private Double companyAddressMapLocationLattitude;
    private Double companyAddressMapLocationLongtitude;

    private String companyAddressState;

    private boolean companyIsActive;

    private boolean companyIsArchived;

    private Long activeJobCount;
    private String emailDomainHost;
    private String emailDomainUsername;
    private String emailDomainPassword;

}