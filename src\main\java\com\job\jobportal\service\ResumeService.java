package com.job.jobportal.service;

import com.job.jobportal.dto.*;
import com.job.jobportal.model.*;
import com.job.jobportal.repository.*;
import com.job.jobportal.util.CommonUtils;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class ResumeService {

    @Autowired
    private ResumeRepository resumeRepository;
    @Autowired
    private EducationRepository educationRepository;
    @Autowired
    private WorkExperienceRepository workExperienceRepository;
    @Autowired
    private AwardRepository awardRepository;
    @Autowired
    private CandidateProfileRepo candidateProfileRepo;
    @Autowired
    private ModelMapper modelMapper;
    @Autowired
    private MessageSource messageSource;

    private static final Logger logger = LoggerFactory.getLogger(ResumeService.class);

    // Temporary storage for delete confirmation tokens (in-memory for simplicity)
    private final Map<Long, String> deleteConfirmationTokens = new HashMap<>();

    private CandidateProfile getCandidateProfile() {
        Long userId = CommonUtils.getUserPrincipal().getId();
        logger.debug("Fetching candidate profile for user ID: {}", userId);
        return candidateProfileRepo.findByRegistereduser_Userid(userId)
                .orElseThrow(() -> {
                    logger.error("Candidate profile not found for user ID: {}", userId);
                    return new RuntimeException(
                            messageSource.getMessage("msg.candidate_profile_fetched", null, Locale.getDefault())
                    );
                });
    }

    @Transactional
    public ResumeDTO createResume(ResumeDTO resumeDTO) {
        logger.info("Creating resume with title: {}", resumeDTO.getTitle());
        CandidateProfile profile = getCandidateProfile();

        if (resumeRepository.existsByCandidateProfile_CandidateProfileIdAndTitle(
                profile.getCandidateProfileId(), resumeDTO.getTitle())) {
            logger.warn("Resume with title '{}' already exists for candidate profile ID: {}",
                    resumeDTO.getTitle(), profile.getCandidateProfileId());
            throw new IllegalArgumentException(
                    messageSource.getMessage("msg.resume.title_exists", null, Locale.getDefault())
            );
        }

        Resume resume = modelMapper.map(resumeDTO, Resume.class);
        resume.setCandidateProfile(profile);
        resume.setIsActive(true);
        Resume savedResume = resumeRepository.save(resume);
        logger.info("Resume created successfully with ID: {}", savedResume.getResumeId());
        return modelMapper.map(savedResume, ResumeDTO.class);
    }

    @Transactional(readOnly = true)
    public List<ResumeDTO> getAllResumes() {
        logger.info("Fetching all resumes for candidate profile");
        CandidateProfile profile = getCandidateProfile();
        List<Resume> resumes = resumeRepository.findByCandidateProfile_CandidateProfileId(profile.getCandidateProfileId());
        logger.debug("Found {} resumes for candidate profile ID: {}", resumes.size(), profile.getCandidateProfileId());
        return resumes.stream()
                .map(resume -> modelMapper.map(resume, ResumeDTO.class))
                .collect(Collectors.toList());
    }

    @Transactional
    public ResumeDTO updateResume(Long resumeId, ResumeDTO resumeDTO) {
        logger.info("Updating resume with ID: {}", resumeId);
        Resume existingResume = getResumeByIdAndCandidate(resumeId);

        modelMapper.typeMap(ResumeDTO.class, Resume.class)
                .addMappings(mapper -> {
                    mapper.skip(Resume::setAwards);
                    mapper.skip(Resume::setWorkExperiences);
                    mapper.skip(Resume::setEducationEntries);
                });
        modelMapper.map(resumeDTO, existingResume);

        logger.debug("Mapped ResumeDTO to existing resume with ID: {}", resumeId);
        Resume updatedResume = resumeRepository.save(existingResume);
        logger.info("Resume updated successfully with ID: {}", updatedResume.getResumeId());
        return modelMapper.map(updatedResume, ResumeDTO.class);
    }

    @Transactional(readOnly = true)
    public String requestDeleteResume(Long resumeId) {
        logger.info("Requesting delete confirmation for resume ID: {}", resumeId);
        Resume resume = getResumeByIdAndCandidate(resumeId);
        String token = UUID.randomUUID().toString();
        deleteConfirmationTokens.put(resumeId, token);
        logger.debug("Generated delete confirmation token: {} for resume ID: {}", token, resumeId);
        return token;
    }

    @Transactional
    public void deleteResume(Long resumeId, String confirmationToken) {
        logger.info("Attempting to delete resume with ID: {} using token: {}", resumeId, confirmationToken);
        Resume resume = getResumeByIdAndCandidate(resumeId);

        String storedToken = deleteConfirmationTokens.get(resumeId);
        if (storedToken == null || !storedToken.equals(confirmationToken)) {
            logger.warn("Invalid or missing confirmation token for resume ID: {}", resumeId);
            throw new IllegalArgumentException(
                    messageSource.getMessage("msg.delete_confirmation_invalid", null, "Invalid delete confirmation", Locale.getDefault())
            );
        }

        resumeRepository.delete(resume);
        deleteConfirmationTokens.remove(resumeId);
        logger.info("Resume deleted successfully with ID: {}", resumeId);
    }

    @Transactional
    public EducationDTO addEducation(Long resumeId, EducationDTO educationDTO) {
        logger.info("Adding education to resume ID: {}", resumeId);
        Resume resume = getResumeByIdAndCandidate(resumeId);
        EducationEntry entry = modelMapper.map(educationDTO, EducationEntry.class);
        entry.setResume(resume);
        EducationEntry savedEntry = educationRepository.save(entry);
        logger.info("Education added successfully with ID: {} to resume ID: {}", savedEntry.getEducationId(), resumeId);
        return modelMapper.map(savedEntry, EducationDTO.class);
    }

    @Transactional
    public WorkExperienceDTO addWorkExperience(Long resumeId, WorkExperienceDTO workDTO) {
        logger.info("Adding work experience to resume ID: {}", resumeId);
        Resume resume = getResumeByIdAndCandidate(resumeId);
        WorkExperience entry = modelMapper.map(workDTO, WorkExperience.class);
        entry.setResume(resume);
        WorkExperience savedEntry = workExperienceRepository.save(entry);
        logger.info("Work experience added successfully with ID: {} to resume ID: {}", savedEntry.getWorkExperienceId(), resumeId);
        return modelMapper.map(savedEntry, WorkExperienceDTO.class);
    }

    @Transactional
    public AwardDTO addAward(Long resumeId, AwardDTO awardDTO) {
        logger.info("Adding award to resume ID: {}", resumeId);
        Resume resume = getResumeByIdAndCandidate(resumeId);
        Award entry = modelMapper.map(awardDTO, Award.class);
        entry.setResume(resume);
        Award savedEntry = awardRepository.save(entry);
        logger.info("Award added successfully with ID: {} to resume ID: {}", savedEntry.getAwardId(), resumeId);
        return modelMapper.map(savedEntry, AwardDTO.class);
    }

    @Transactional
    public EducationDTO updateEducation(Long resumeId, Long educationId, EducationDTO educationDTO) {
        logger.info("Updating education with ID: {} for resume ID: {}", educationId, resumeId);
        EducationEntry entry = educationRepository.findByResumeIdAndEducationId(resumeId, educationId)
                .orElseThrow(() -> {
                    logger.error("Education not found with ID: {} for resume ID: {}", educationId, resumeId);
                    return new RuntimeException(
                            messageSource.getMessage("msg.education.not_found", null, Locale.getDefault())
                    );
                });

        validateEducationDates(educationDTO.getStartDate(), educationDTO.getEndDate());
        modelMapper.map(educationDTO, entry);
        EducationEntry updatedEntry = educationRepository.save(entry);
        logger.info("Education updated successfully with ID: {}", updatedEntry.getEducationId());
        return modelMapper.map(updatedEntry, EducationDTO.class);
    }

    @Transactional
    public void deleteEducation(Long resumeId, Long educationId) {
        logger.info("Deleting education with ID: {} from resume ID: {}", educationId, resumeId);
        EducationEntry entry = educationRepository.findByResumeIdAndEducationId(resumeId, educationId)
                .orElseThrow(() -> {
                    logger.error("Education not found with ID: {} for resume ID: {}", educationId, resumeId);
                    return new RuntimeException(
                            messageSource.getMessage("msg.education.not_found", null, Locale.getDefault())
                    );
                });
        educationRepository.delete(entry);
        logger.info("Education deleted successfully with ID: {}", educationId);
    }

    @Transactional
    public WorkExperienceDTO updateWorkExperience(Long resumeId, Long workId, WorkExperienceDTO workDTO) {
        logger.info("Updating work experience with ID: {} for resume ID: {}", workId, resumeId);
        WorkExperience entry = workExperienceRepository.findByResumeIdAndWorkId(resumeId, workId)
                .orElseThrow(() -> {
                    logger.error("Work experience not found with ID: {} for resume ID: {}", workId, resumeId);
                    return new RuntimeException(
                            messageSource.getMessage("msg.work_experience.not_found", null, Locale.getDefault())
                    );
                });

        validateWorkDates(workDTO.getStartDate(), workDTO.getEndDate());
        modelMapper.map(workDTO, entry);
        WorkExperience updatedEntry = workExperienceRepository.save(entry);
        logger.info("Work experience updated successfully with ID: {}", updatedEntry.getWorkExperienceId());
        return modelMapper.map(updatedEntry, WorkExperienceDTO.class);
    }

    @Transactional
    public void deleteWorkExperience(Long resumeId, Long workId) {
        logger.info("Deleting work experience with ID: {} from resume ID: {}", workId, resumeId);
        WorkExperience entry = workExperienceRepository.findByResumeIdAndWorkId(resumeId, workId)
                .orElseThrow(() -> {
                    logger.error("Work experience not found with ID: {} for resume ID: {}", workId, resumeId);
                    return new RuntimeException(
                            messageSource.getMessage("msg.work_experience.not_found", null, Locale.getDefault())
                    );
                });
        workExperienceRepository.delete(entry);
        logger.info("Work experience deleted successfully with ID: {}", workId);
    }

    @Transactional
    public AwardDTO updateAward(Long resumeId, Long awardId, AwardDTO awardDTO) {
        logger.info("Updating award with ID: {} for resume ID: {}", awardId, resumeId);
        Award entry = awardRepository.findByResumeIdAndAwardId(resumeId, awardId)
                .orElseThrow(() -> {
                    logger.error("Award not found with ID: {} for resume ID: {}", awardId, resumeId);
                    return new RuntimeException(
                            messageSource.getMessage("msg.award.not_found", null, Locale.getDefault())
                    );
                });

        validateAwardDate(awardDTO.getIssueDate());
        modelMapper.map(awardDTO, entry);
        Award updatedEntry = awardRepository.save(entry);
        logger.info("Award updated successfully with ID: {}", updatedEntry.getAwardId());
        return modelMapper.map(updatedEntry, AwardDTO.class);
    }

    @Transactional
    public void deleteAward(Long resumeId, Long awardId) {
        logger.info("Deleting award with ID: {} from resume ID: {}", awardId, resumeId);
        Award entry = awardRepository.findByResumeIdAndAwardId(resumeId, awardId)
                .orElseThrow(() -> {
                    logger.error("Award not found with ID: {} for resume ID: {}", awardId, resumeId);
                    return new RuntimeException(
                            messageSource.getMessage("msg.award.not_found", null, Locale.getDefault())
                    );
                });
        awardRepository.delete(entry);
        logger.info("Award deleted successfully with ID: {}", awardId);
    }

    private void validateEducationDates(LocalDate startDate, LocalDate endDate) {
        if (endDate != null && endDate.isBefore(startDate)) {
            logger.warn("Validation failed: End date {} is before start date {}", endDate, startDate);
            throw new IllegalArgumentException(
                    messageSource.getMessage("msg.validation.end_date_before_start", null, Locale.getDefault())
            );
        }
    }

    private void validateWorkDates(LocalDate startDate, LocalDate endDate) {
        if (endDate != null && endDate.isBefore(startDate)) {
            logger.warn("Validation failed: End date {} is before start date {}", endDate, startDate);
            throw new IllegalArgumentException(
                    messageSource.getMessage("msg.validation.end_date_before_start", null, Locale.getDefault())
            );
        }
    }

    private void validateAwardDate(LocalDate issueDate) {
        if (issueDate.isAfter(LocalDate.now())) {
            logger.warn("Validation failed: Award issue date {} is in the future", issueDate);
            throw new IllegalArgumentException(
                    messageSource.getMessage("msg.validation.future_date", null, Locale.getDefault())
            );
        }
    }

    private Resume getResumeByIdAndCandidate(Long resumeId) {
        logger.debug("Fetching resume with ID: {} for candidate", resumeId);
        CandidateProfile profile = getCandidateProfile();
        return resumeRepository.findById(resumeId)
                .filter(r -> r.getCandidateProfile().getCandidateProfileId().equals(profile.getCandidateProfileId()))
                .orElseThrow(() -> {
                    logger.error("Resume not found with ID: {} for candidate profile ID: {}",
                            resumeId, profile.getCandidateProfileId());
                    return new RuntimeException(
                            messageSource.getMessage("msg.resume.not_found", null, Locale.getDefault())
                    );
                });
    }
}