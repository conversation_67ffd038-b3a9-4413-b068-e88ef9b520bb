package com.job.jobportal.util;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class PermissionUtil {

    public static boolean hasPermission(String permissionString, int permissionPosition) {
        if (permissionString == null || permissionString.isEmpty() ||
            permissionPosition < 0 || permissionPosition >= permissionString.length()) {
            return false;
        }

        return permissionString.charAt(permissionPosition) == '1';
    }

    public static Map<Integer, Boolean> parsePermissions(String permissionString) {
        Map<Integer, Boolean> result = new HashMap<>();

        if (permissionString == null || permissionString.isEmpty()) {
            return result;
        }

        for (int i = 0; i < permissionString.length(); i++) {
            boolean enabled = permissionString.charAt(i) == '1';
            result.put(i, enabled);
        }

        return result;
    }

    public static String createPermissionString(Map<Integer, Boolean> permissionValues, int length) {
        if (length <= 0) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            Boolean value = permissionValues.get(i);
            sb.append(value != null && value ? '1' : '0');
        }

        return sb.toString();
    }

    public static Map<String, Boolean> getPermissionMap(String permissionString) {
        Map<String, Boolean> result = new HashMap<>();

        if (permissionString == null || permissionString.isEmpty()) {
            return result;
        }

        if (permissionString.length() > ConstantsUtil.PERMISSION_VIEW_APPLICANTS) {
            result.put("VIEW_APPLICANTS", permissionString.charAt(ConstantsUtil.PERMISSION_VIEW_APPLICANTS) == '1');
        }

        if (permissionString.length() > ConstantsUtil.PERMISSION_CONTACT_APPLICANTS) {
            result.put("CONTACT_APPLICANTS", permissionString.charAt(ConstantsUtil.PERMISSION_CONTACT_APPLICANTS) == '1');
        }

        if (permissionString.length() > ConstantsUtil.PERMISSION_POST_JOBS) {
            result.put("POST_JOBS", permissionString.charAt(ConstantsUtil.PERMISSION_POST_JOBS) == '1');
        }

        if (permissionString.length() > ConstantsUtil.PERMISSION_FEATURED_JOBS) {
            result.put("FEATURED_JOBS", permissionString.charAt(ConstantsUtil.PERMISSION_FEATURED_JOBS) == '1');
        }

        if (permissionString.length() > ConstantsUtil.PERMISSION_ANALYTICS) {
            result.put("ANALYTICS", permissionString.charAt(ConstantsUtil.PERMISSION_ANALYTICS) == '1');
        }

        if (permissionString.length() > ConstantsUtil.PERMISSION_BULK_ACTIONS) {
            result.put("BULK_ACTIONS", permissionString.charAt(ConstantsUtil.PERMISSION_BULK_ACTIONS) == '1');
        }

        return result;
    }
}
