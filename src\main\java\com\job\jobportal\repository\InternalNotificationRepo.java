package com.job.jobportal.repository;

import com.job.jobportal.model.InternalNotification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface InternalNotificationRepo extends JpaRepository<InternalNotification, Long> {

//    @Query("SELECT dn FROM InternalNotification dn " +
//            "JOIN  FETCH dn.profile p " +
//            "JOIN AddFamily af ON p.profileId = af.profile.profileId " +
//            "WHERE af.registereduser.userid = :userid  Order by dn.notificationDate")
//    List<InternalNotification> findNotificationByUserId(@Param("userid") Long userId);


}
