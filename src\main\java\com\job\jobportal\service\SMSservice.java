package com.job.jobportal.service;

import com.twilio.Twilio;
import com.twilio.rest.api.v2010.account.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class SMSservice {

//    @Value("${sms.TWILIO_ACCOUNT_SID}")
//    private String ACCOUNT_SID;
//
//    @Value("${sms.TWILIO_AUTH_TOKEN}")
//    private String AUTH_TOKEN;
//
//    @Value("${sms.PHONE_NUMBER}")
//    private String PHONE_NUMBER;

//    private static final Logger logger = LoggerFactory.getLogger(SMSservice.class);
//
//
//    public void sendSMS(String toPhoneNumber, String contentMessage) {
//        try {
//            Twilio.init(ACCOUNT_SID, AUTH_TOKEN);
//            Message message = Message.creator(
//
//                            new com.twilio.type.PhoneNumber(toPhoneNumber),
//                            new com.twilio.type.PhoneNumber(PHONE_NUMBER),
//                            contentMessage)
//                    .create();
//
//            System.out.println(message.getSid());
//
//        } catch (Exception e) {
//
//            logger.error(e.getMessage());
//            throw e;
//        }
//
//    }
//
//    public void sendWhatsApp(String toPhoneNumber, String contentMessage) {
//        try {
//            Twilio.init(ACCOUNT_SID, AUTH_TOKEN);
//            Message message = Message.creator(
//
//                            new com.twilio.type.PhoneNumber("whatsapp:" + toPhoneNumber),
//                            new com.twilio.type.PhoneNumber("whatsapp:+***********"),
//                            contentMessage)
//                    .create();
//
//            System.out.println(message.getSid());
//
//        } catch (Exception e) {
//
//            logger.error(e.getMessage());
//            throw e;
//        }
//
//    }
}
