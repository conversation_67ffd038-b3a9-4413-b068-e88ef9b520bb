package com.job.jobportal.repository;

import jakarta.persistence.EntityManager;
import jakarta.persistence.ParameterMode;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.StoredProcedureQuery;

import java.sql.Timestamp;

public class ProfileStoredProcedureImpl implements ProfileStoredProcedure {

    @PersistenceContext
    private EntityManager entityManager;


    @Override
    public Long createProfile(String profileName, Timestamp profileDOB, long userId, int gender, String relationShip) {
        StoredProcedureQuery query = entityManager.createStoredProcedureQuery("addProfile");
        query.registerStoredProcedureParameter("inprofileName", String.class, ParameterMode.IN);
        query.registerStoredProcedureParameter("inprofileDOB", Timestamp.class, ParameterMode.IN);
        query.registerStoredProcedureParameter("inuserId", Long.class, ParameterMode.IN);
        query.registerStoredProcedureParameter("ingender", Integer.class, ParameterMode.IN);
        query.registerStoredProcedureParameter("inrelationShip", String.class, ParameterMode.IN);
        query.registerStoredProcedureParameter("outprofileId", Long.class, ParameterMode.OUT);

        query.setParameter("inprofileName", profileName);
        query.setParameter("inprofileDOB", profileDOB);
        query.setParameter("inuserId", userId);
        query.setParameter("ingender", gender);
        query.setParameter("inrelationShip", relationShip);

        query.execute();

        return (Long) query.getOutputParameterValue("outprofileId");
    }
}
