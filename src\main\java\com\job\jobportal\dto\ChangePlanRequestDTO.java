package com.job.jobportal.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChangePlanRequestDTO {

    @NotBlank(message = "Plan name is required")
    private String planName;

    @NotNull(message = "Billing cycle preference is required")
    private Boolean isYearly;
}
