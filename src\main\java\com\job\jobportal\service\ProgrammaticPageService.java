package com.job.jobportal.service;

import com.job.jobportal.dto.CityStateDTO;
import com.job.jobportal.dto.MasterDataDto;
import com.job.jobportal.dto.ProgrammaticPageDTO;
import com.job.jobportal.dto.SeoGenerateRequest;
import com.job.jobportal.dto.SeoGenerateResponse;
import com.job.jobportal.model.MasterData;
import com.job.jobportal.model.ProgrammaticPage;
import com.job.jobportal.repository.JobRepository;
import com.job.jobportal.repository.MasterDataRepository;
import com.job.jobportal.repository.ProgrammaticPageRepo;

import com.job.jobportal.util.CommonUtils;
import com.job.jobportal.util.ConstantsUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


@Service
public class ProgrammaticPageService {

    @Autowired
    private ProgrammaticPageRepo programmaticPageRepo;

    @Autowired
    private MasterDataRepository masterDataRepository;

    @Autowired
    private SeoGenerateService seoGenerateService;

    @Autowired
    private MasterDataService masterDataService;


    @Autowired
    private JobRepository jobRepository;

    private static final Logger logger = LoggerFactory.getLogger(ProgrammaticPageService.class);

    public ProgrammaticPage addProgrammaticPage(ProgrammaticPageDTO dto) {
        try {
            Integer categoryId = dto.getProgrammaticPageCategoryId();
            Integer subcategoryId = dto.getProgrammaticPageSubcategoryId();
            Integer subSubcategoryId = dto.getProgrammaticPageSubSubcategoryId();
            String city = dto.getProgrammaticPageCity();
            String district = dto.getProgrammaticPageDistrict();

            String categoryName = getCategoryName(categoryId);
            String subcategoryName = getSubcategoryName(subcategoryId);
            String subSubcategoryName = getSubSubcategoryName(subSubcategoryId);

            String generatedUrl = generateProgrammaticUrl(categoryName, subcategoryName, subSubcategoryName, city);
            Optional<ProgrammaticPage> existingPageWithSameUrl = programmaticPageRepo.findByProgrammaticPageUrl(generatedUrl);

            if (existingPageWithSameUrl.isPresent()) {
                String pattern = determineSearchPattern(categoryName, subcategoryName, subSubcategoryName, city);
                logger.error("Cannot create programmatic page: A page with the URL '{}' already exists (pattern: {})", generatedUrl, pattern);
                throw new RuntimeException("Record is already present for this pattern: " + pattern);
            }

            List<ProgrammaticPage> existingPages = programmaticPageRepo.findAll().stream()
                .filter(p -> {
                    boolean categoryMatch = (categoryId == null && p.getProgrammaticPageCategoryId() == null) ||
                                         (categoryId != null && p.getProgrammaticPageCategoryId() != null &&
                                          categoryId.equals(p.getProgrammaticPageCategoryId()));

                    boolean subcategoryMatch = (subcategoryId == null && p.getProgrammaticPageSubcategoryId() == null) ||
                                            (subcategoryId != null && p.getProgrammaticPageSubcategoryId() != null &&
                                             subcategoryId.equals(p.getProgrammaticPageSubcategoryId()));

                    boolean subSubcategoryMatch = (subSubcategoryId == null && p.getProgrammaticPageSubSubcategoryId() == null) ||
                                               (subSubcategoryId != null && p.getProgrammaticPageSubSubcategoryId() != null &&
                                                subSubcategoryId.equals(p.getProgrammaticPageSubSubcategoryId()));

                    boolean cityMatch = (city == null && p.getProgrammaticPageCity() == null) ||
                                      (city != null && p.getProgrammaticPageCity() != null &&
                                       city.equalsIgnoreCase(p.getProgrammaticPageCity()));


                    return categoryMatch && subcategoryMatch && subSubcategoryMatch && cityMatch;
                })
                .toList();

            if (!existingPages.isEmpty()) {
                String pattern = determineSearchPattern(categoryName, subcategoryName, subSubcategoryName, city);
                logger.error("Cannot create programmatic page: A page with the pattern {} already exists", pattern);
                throw new RuntimeException("Record is already present for this pattern: " + pattern);
            }

            ProgrammaticPage page = new ProgrammaticPage();
            page.setProgrammaticPageTitle(dto.getProgrammaticPageTitle());

            page.setProgrammaticPageCategoryId(dto.getProgrammaticPageCategoryId());
            page.setProgrammaticPageSubcategoryId(dto.getProgrammaticPageSubcategoryId());
            page.setProgrammaticPageSubSubcategoryId(dto.getProgrammaticPageSubSubcategoryId());
            page.setProgrammaticPageCityId(dto.getProgrammaticPageCityId());
            page.setProgrammaticPageCity(dto.getProgrammaticPageCity());
            page.setProgrammaticPageDistrictId(dto.getProgrammaticPageDistrictId());
            page.setProgrammaticPageDistrict(dto.getProgrammaticPageDistrict());

            if (dto.getProgrammaticPageUrl() == null || dto.getProgrammaticPageUrl().isEmpty()) {
                String url = generateProgrammaticUrl(categoryName, subcategoryName, subSubcategoryName,
                        dto.getProgrammaticPageCity());
                page.setProgrammaticPageUrl(url);
            } else {
                page.setProgrammaticPageUrl(dto.getProgrammaticPageUrl());
            }

            if (dto.getProgrammaticPageMetaDescription() != null && !dto.getProgrammaticPageMetaDescription().isEmpty()) {
                logger.info("Using user-provided meta description for programmatic page");
                page.setProgrammaticPageMetaDescription(dto.getProgrammaticPageMetaDescription());

                if (dto.getProgrammaticPageMetaTitle() != null && !dto.getProgrammaticPageMetaTitle().isEmpty()) {
                    page.setProgrammaticPageMetaTitle(dto.getProgrammaticPageMetaTitle());
                } else {
                    SeoGenerateResponse seoMetadata = generateSeoMetadata(dto);
                    page.setProgrammaticPageMetaTitle(seoMetadata.getMetaTitle());
                }
            } else {
                SeoGenerateResponse seoMetadata = generateSeoMetadata(dto);
                page.setProgrammaticPageMetaTitle(seoMetadata.getMetaTitle());
                page.setProgrammaticPageMetaDescription(seoMetadata.getMetaDescription());
            }

            page.setProgrammaticPageContent(dto.getProgrammaticPageContent());
            page.setProgrammaticPageCreatedDate(CommonUtils.getCurrentTime());
            page.setProgrammaticPageUpdatedDate(CommonUtils.getCurrentTime());
            page.setProgrammaticPageIsActive(true);

            ProgrammaticPage savedPage = programmaticPageRepo.save(page);

            logger.info("Created programmatic page with ID {} and SEO metadata", savedPage.getProgrammaticPageId());
            return savedPage;
        } catch (Exception e) {
            logger.error(e.getMessage());
            throw e;
        }
    }

    public ProgrammaticPage updateProgrammaticPage(ProgrammaticPageDTO dto) {
        try {
            ProgrammaticPage existingPage = programmaticPageRepo.findById(dto.getProgrammaticPageId())
                    .orElseThrow(() -> new RuntimeException("Programmatic page not found with id: " + dto.getProgrammaticPageId()));

            Integer categoryId = dto.getProgrammaticPageCategoryId();
            Integer subcategoryId = dto.getProgrammaticPageSubcategoryId();
            Integer subSubcategoryId = dto.getProgrammaticPageSubSubcategoryId();
            String city = dto.getProgrammaticPageCity();
            String district = dto.getProgrammaticPageDistrict();

            String categoryName = getCategoryName(categoryId);
            String subcategoryName = getSubcategoryName(subcategoryId);
            String subSubcategoryName = getSubSubcategoryName(subSubcategoryId);

            String generatedUrl = generateProgrammaticUrl(categoryName, subcategoryName, subSubcategoryName, city);
            Optional<ProgrammaticPage> existingPageWithSameUrl = programmaticPageRepo.findByProgrammaticPageUrl(generatedUrl);

            if (existingPageWithSameUrl.isPresent() && !existingPageWithSameUrl.get().getProgrammaticPageId().equals(dto.getProgrammaticPageId())) {
                String pattern = determineSearchPattern(categoryName, subcategoryName, subSubcategoryName, city);
                logger.error("Cannot update programmatic page: Another page with the URL '{}' already exists (pattern: {})", generatedUrl, pattern);
                throw new RuntimeException("Record is already present for this pattern: " + pattern);
            }

            List<ProgrammaticPage> existingPages = programmaticPageRepo.findAll().stream()
                .filter(p -> {
                    if (p.getProgrammaticPageId().equals(dto.getProgrammaticPageId())) {
                        return false;
                    }

                    boolean categoryMatch = (categoryId == null && p.getProgrammaticPageCategoryId() == null) ||
                                         (categoryId != null && p.getProgrammaticPageCategoryId() != null &&
                                          categoryId.equals(p.getProgrammaticPageCategoryId()));

                    boolean subcategoryMatch = (subcategoryId == null && p.getProgrammaticPageSubcategoryId() == null) ||
                                            (subcategoryId != null && p.getProgrammaticPageSubcategoryId() != null &&
                                             subcategoryId.equals(p.getProgrammaticPageSubcategoryId()));

                    boolean subSubcategoryMatch = (subSubcategoryId == null && p.getProgrammaticPageSubSubcategoryId() == null) ||
                                               (subSubcategoryId != null && p.getProgrammaticPageSubSubcategoryId() != null &&
                                                subSubcategoryId.equals(p.getProgrammaticPageSubSubcategoryId()));

                    boolean cityMatch = (city == null && p.getProgrammaticPageCity() == null) ||
                                      (city != null && p.getProgrammaticPageCity() != null &&
                                       city.equalsIgnoreCase(p.getProgrammaticPageCity()));


                    return categoryMatch && subcategoryMatch && subSubcategoryMatch && cityMatch;
                })
                .toList();

            if (!existingPages.isEmpty()) {
                String pattern = determineSearchPattern(categoryName, subcategoryName, subSubcategoryName, city);
                logger.error("Cannot update programmatic page: Another page with the pattern {} already exists", pattern);
                throw new RuntimeException("Record is already present for this pattern: " + pattern);
            }

            ProgrammaticPage page = new ProgrammaticPage();
            page.setProgrammaticPageId(dto.getProgrammaticPageId());
            page.setProgrammaticPageTitle(dto.getProgrammaticPageTitle());

            page.setProgrammaticPageCategoryId(dto.getProgrammaticPageCategoryId());
            page.setProgrammaticPageSubcategoryId(dto.getProgrammaticPageSubcategoryId());
            page.setProgrammaticPageSubSubcategoryId(dto.getProgrammaticPageSubSubcategoryId());
            page.setProgrammaticPageCityId(dto.getProgrammaticPageCityId());
            page.setProgrammaticPageCity(dto.getProgrammaticPageCity());
            page.setProgrammaticPageDistrictId(dto.getProgrammaticPageDistrictId());
            page.setProgrammaticPageDistrict(dto.getProgrammaticPageDistrict());

            if (dto.getProgrammaticPageUrl() == null || dto.getProgrammaticPageUrl().isEmpty()) {
                String url = generateProgrammaticUrl(categoryName, subcategoryName, subSubcategoryName,
                        dto.getProgrammaticPageCity());
                page.setProgrammaticPageUrl(url);
            } else {
                page.setProgrammaticPageUrl(dto.getProgrammaticPageUrl());
            }

            if (dto.getProgrammaticPageMetaDescription() != null && !dto.getProgrammaticPageMetaDescription().isEmpty()) {
                logger.info("Using user-provided meta description for programmatic page update");
                page.setProgrammaticPageMetaDescription(dto.getProgrammaticPageMetaDescription());

                if (dto.getProgrammaticPageMetaTitle() != null && !dto.getProgrammaticPageMetaTitle().isEmpty()) {
                    page.setProgrammaticPageMetaTitle(dto.getProgrammaticPageMetaTitle());
                } else {
                    SeoGenerateResponse seoMetadata = generateSeoMetadata(dto);
                    page.setProgrammaticPageMetaTitle(seoMetadata.getMetaTitle());
                }
            } else {
                SeoGenerateResponse seoMetadata = generateSeoMetadata(dto);
                page.setProgrammaticPageMetaTitle(seoMetadata.getMetaTitle());
                page.setProgrammaticPageMetaDescription(seoMetadata.getMetaDescription());
            }

            page.setProgrammaticPageContent(dto.getProgrammaticPageContent());
            page.setProgrammaticPageCreatedDate(dto.getProgrammaticPageCreatedDate());
            page.setProgrammaticPageUpdatedDate(CommonUtils.getCurrentTime());
            page.setProgrammaticPageIsActive(dto.getProgrammaticPageIsActive());

            ProgrammaticPage updatedPage = programmaticPageRepo.save(page);

            logger.info("Updated programmatic page with ID {} and SEO metadata", updatedPage.getProgrammaticPageId());
            return updatedPage;
        } catch (Exception e) {
            logger.error(e.getMessage());
            throw e;
        }
    }

    public void deleteProgrammaticPage(Long id) {
        try {
            programmaticPageRepo.deleteById(id);
        } catch (Exception e) {
            logger.error(e.getMessage());
            throw e;
        }
    }

    public Map<String, Object> getAllProgrammaticPages(int page, int size, String title) {
        try {
            Pageable pagingSort = PageRequest.of(page, size);
            Page<ProgrammaticPage> pages = programmaticPageRepo.findAllProgrammaticPages(title, pagingSort);

            Map<String, Object> map = new HashMap<>();
            map.put("pages", pages.getContent());
            map.put("currentPage", pages.getNumber());
            map.put("totalItems", pages.getTotalElements());
            map.put("totalPages", pages.getTotalPages());
            return map;
        } catch (Exception e) {
            logger.error(e.getMessage());
            throw e;
        }
    }

    public ProgrammaticPage getProgrammaticPageById(Long id) {
        return programmaticPageRepo.findById(id)
                .orElseThrow(() -> new RuntimeException("Programmatic page not found with id: " + id));
    }

    public ProgrammaticPage getProgrammaticPageByUrl(String url) {
        logger.info("Looking for programmatic page with exact URL: {}", url);
        return programmaticPageRepo.findByProgrammaticPageUrl(url)
                .orElseThrow(() -> {
                    logger.warn("No programmatic page found with exact URL: {}", url);
                    return new RuntimeException("Programmatic page not found with URL: " + url);
                });
    }
    public List<ProgrammaticPage> getProgrammaticPageByCategorySubcategoryCountry(String category, String subcategory, String subSubcategory, String city) {
        try {
            logger.info("Searching for programmatic pages with raw parameters: category='{}', subcategory='{}', subSubcategory='{}', city='{}'",
                    category, subcategory, subSubcategory, city);

            logger.info("Parameter lengths: category={}, subcategory={}, subSubcategory={}, city={}",
                    category != null ? category.length() : -1,
                    subcategory != null ? subcategory.length() : -1,
                    subSubcategory != null ? subSubcategory.length() : -1,
                    city != null ? city.length() : -1);

            String rawPattern = determineSearchPattern(category, subcategory, subSubcategory, city);
            logger.info("Raw search pattern (before normalization): {}", rawPattern);

            String categoryParam = isNullOrEmpty(category) ? null : category;
            String subcategoryParam = isNullOrEmpty(subcategory) ? null : subcategory;
            String subSubcategoryParam = isNullOrEmpty(subSubcategory) ? null : subSubcategory;
            String cityParam = isNullOrEmpty(city) ? null : city;

            String pattern = determineSearchPattern(categoryParam, subcategoryParam, subSubcategoryParam, cityParam);
            logger.info("Normalized search pattern: {}", pattern);

            boolean allParamsEmpty = categoryParam == null && subcategoryParam == null &&
                                    subSubcategoryParam == null && cityParam == null;

            if (allParamsEmpty) {
                logger.info("No search parameters provided, returning all programmatic pages");
                List<ProgrammaticPage> allPages = programmaticPageRepo.findAll();
                logger.info("Found {} total programmatic pages", allPages.size());
                return allPages;
            }

            logger.info("Using normalized parameters for repository query: category={}, subcategory={}, subSubcategory={}, city={}",
                    categoryParam, subcategoryParam, subSubcategoryParam, cityParam);

            List<ProgrammaticPage> exactMatches = findAllMatchingPages(categoryParam, subcategoryParam, subSubcategoryParam, cityParam);

            if (!exactMatches.isEmpty()) {
                logger.info("Found {} exact matching programmatic pages for pattern: {}", exactMatches.size(), pattern);
                return exactMatches;
            }

            logger.warn("No programmatic pages found with the specified pattern: {}", pattern);
            return new ArrayList<>();
        } catch (Exception e) {
            logger.error("Error searching for programmatic pages: {}", e.getMessage());
            throw e;
        }
    }

    private List<ProgrammaticPage> findAllMatchingPages(String category, String subcategory, String subSubcategory, String city) {
        return programmaticPageRepo.findAllByCategorySubcategorySubSubcategoryAndLocation(
                category, subcategory, subSubcategory, city);
    }

    private Optional<ProgrammaticPage> findExactMatch(String category, String subcategory, String subSubcategory, String city) {
        return programmaticPageRepo.findByCategorySubcategorySubSubcategoryAndLocation(
                category, subcategory, subSubcategory, city);
    }

    private String determineSearchPattern(String category, String subcategory, String subSubcategory, String city) {
        StringBuilder pattern = new StringBuilder();

        if (!isNullOrEmpty(category)) {
            pattern.append("category");
        }

        if (!isNullOrEmpty(subcategory)) {
            if (!pattern.isEmpty()) pattern.append("+");
            pattern.append("subcategory");
        }

        if (!isNullOrEmpty(subSubcategory)) {
            if (!pattern.isEmpty()) pattern.append("+");
            pattern.append("subSubcategory");
        }

        if (!isNullOrEmpty(city)) {
            if (!pattern.isEmpty()) pattern.append("+");
            pattern.append("city");
        }

        return !pattern.isEmpty() ? pattern.toString() : "all";
    }


    private boolean isNullOrEmpty(String str) {
        return str == null || str.isEmpty() || str.trim().isEmpty();
    }


    private String getCategoryName(Integer categoryId) {
        if (categoryId == null) {
            return "";
        }

        MasterData category = masterDataRepository.findByComponentType_IdAndMasterDataId(
                ConstantsUtil.JOB_CATEGORIES_COMPONENT_TYPE_ID, categoryId);

        return category != null ? category.getValue() : "";
    }


    private String getSubcategoryName(Integer subcategoryId) {
        if (subcategoryId == null) {
            return "";
        }

        MasterData subcategory = masterDataRepository.findByComponentType_IdAndMasterDataId(
                ConstantsUtil.JOB_SUBCATEGORIES_COMPONENT_TYPE_ID, subcategoryId);

        if (subcategory != null && subcategory.getValue() != null) {

            String[] parts = subcategory.getValue().split("\\|");
            if (parts.length > 0) {
                return parts[0];
            }
        }

        return "";
    }

    private String getSubSubcategoryName(Integer subSubcategoryId) {
        if (subSubcategoryId == null) {
            return "";
        }

        MasterData subSubcategory = masterDataRepository.findByComponentType_IdAndMasterDataId(
                ConstantsUtil.JOB_SUBSUBCATEGORIES_COMPONENT_TYPE_ID, subSubcategoryId);

        if (subSubcategory != null && subSubcategory.getValue() != null) {

            String[] parts = subSubcategory.getValue().split("\\|");
            if (parts.length > 0) {
                return parts[0];
            }
        }

        return "";
    }

    private String generateProgrammaticUrl(String category, String subcategory, String subSubcategory, String city) {
        StringBuilder urlBuilder = new StringBuilder();

        if (category != null && !category.isEmpty()) {
            urlBuilder.append(convertToUrlFriendly(category));
        }

        if (subcategory != null && !subcategory.isEmpty()) {
            if (!urlBuilder.isEmpty()) {
                urlBuilder.append("/");
            }
            urlBuilder.append(convertToUrlFriendly(subcategory));
        }

        if (subSubcategory != null && !subSubcategory.isEmpty()) {
            if (!urlBuilder.isEmpty()) {
                urlBuilder.append("/");
            }
            urlBuilder.append(convertToUrlFriendly(subSubcategory));
        }

        if (city != null && !city.isEmpty()) {
            if (!urlBuilder.isEmpty()) {
                urlBuilder.append("/");
            }
            urlBuilder.append(convertToUrlFriendly(city));
        }
        return urlBuilder.toString();
    }


    private String convertToUrlFriendly(String input) {
        if (input == null) {
            return "";
        }

        String result = input.toLowerCase();

        result = result.replaceAll("\\s+", "-");
        result = result.replaceAll("[^a-z0-9-]", "");
        result = result.replaceAll("-+", "-");
        result = result.replaceAll("^-|-$", "");

        return result;
    }

    private SeoGenerateResponse generateSeoMetadata(ProgrammaticPageDTO dto) {
        try {
            logger.info("Generating SEO metadata for programmatic page: {}", dto.getProgrammaticPageTitle());

            String category = getCategoryName(dto.getProgrammaticPageCategoryId());
            String subcategory = getSubcategoryName(dto.getProgrammaticPageSubcategoryId());
            String subSubcategory = getSubSubcategoryName(dto.getProgrammaticPageSubSubcategoryId());

            String jobTitle = dto.getProgrammaticPageTitle();
            if (jobTitle == null || jobTitle.isEmpty()) {
                if (subSubcategory != null && !subSubcategory.isEmpty()) {
                    jobTitle = subSubcategory;
                } else if (subcategory != null && !subcategory.isEmpty()) {
                    jobTitle = subcategory;
                } else {
                    jobTitle = category;
                }
            }

            if (subcategory == null || subcategory.isEmpty()) {
                subcategory = category;
            }

            SeoGenerateRequest seoRequest = new SeoGenerateRequest();
            seoRequest.setCategory(category);
            seoRequest.setSubcategory(subcategory);
            seoRequest.setJobTitle(jobTitle);
            seoRequest.setCity(dto.getProgrammaticPageCity());
            seoRequest.setDistrict(dto.getProgrammaticPageDistrict());

            return seoGenerateService.generateSeoMetadata(seoRequest);
        } catch (Exception e) {
            logger.error("Error generating SEO metadata for programmatic page: {}", e.getMessage(), e);

            String metaDescription = "Browse ";
            String category = getCategoryName(dto.getProgrammaticPageCategoryId());
            if (category != null && !category.isEmpty()) {
                metaDescription += category + " ";
            }
            metaDescription += "job opportunities";

            String city = dto.getProgrammaticPageCity();
            if (city != null && !city.isEmpty()) {
                metaDescription += " in " + city;

                String district = dto.getProgrammaticPageDistrict();
                if (district != null && !district.isEmpty()) {
                    metaDescription += ", " + district;
                }
            }

            metaDescription += ". Find your next career move and apply today.";

            SeoGenerateResponse defaultResponse = new SeoGenerateResponse();
            defaultResponse.setSeoUrl(dto.getProgrammaticPageUrl());
            defaultResponse.setMetaTitle(dto.getProgrammaticPageTitle());
            defaultResponse.setMetaDescription(metaDescription);
            return defaultResponse;
        }
    }



    public List<MasterDataDto> getProgrammaticCategories() {
        try {
            logger.info("Fetching programmatic categories from component {}", ConstantsUtil.JOB_CATEGORIES_COMPONENT_TYPE_ID);
            return masterDataService.getMasterDataByComponentTypeId(ConstantsUtil.JOB_CATEGORIES_COMPONENT_TYPE_ID);
        } catch (Exception e) {
            logger.error("Error fetching programmatic categories: {}", e.getMessage());
            throw new RuntimeException("Error fetching programmatic categories: " + e.getMessage());
        }
    }


    public List<CityStateDTO> getCityStateCombinations() {
        try {
            logger.info("Fetching unique city-state combinations");
            return jobRepository.findDistinctCityStateCombo();
        } catch (Exception e) {
            logger.error("Error fetching city-state combinations: {}", e.getMessage());
            throw new RuntimeException("Error fetching city-state combinations: " + e.getMessage());
        }
    }


    public List<String> getCityStateCombinationsAsArrays() {
        try {
            logger.info("Fetching unique locations as a single array");
            List<CityStateDTO> combinations = jobRepository.findDistinctCityStateCombo();

            List<List<String>> nestedLocations = combinations.stream()
                .map(combo -> {
                    List<String> array = new ArrayList<>();
                    if (combo.getCity() != null && !combo.getCity().isEmpty()) array.add(combo.getCity());
                    if (combo.getState() != null && !combo.getState().isEmpty()) array.add(combo.getState());
                    if (combo.getDistrict() != null && !combo.getDistrict().isEmpty()) array.add(combo.getDistrict());
                    return array;
                })
                .toList();

            List<String> result = nestedLocations.stream()
                .flatMap(List::stream)
                .distinct()
                .sorted()
                .collect(Collectors.toList());

            logger.info("Fetched {} unique locations", result.size());
            return result;
        } catch (Exception e) {
            logger.error("Error fetching locations as array: {}", e.getMessage());
            throw new RuntimeException("Error fetching locations as array: " + e.getMessage());
        }
    }
}
