package com.job.jobportal.dto;

import com.job.jobportal.model.ApplicationStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = true)
public class JobApplicationFullDTO extends JobApplicationDTO {
    private String candidateName;
    private String designation;
    private Long currentSalary;
    private String candidateLocation;
    private String jobCategory;
    private String jobCategoryName;
    private String jobSubCategory;
    private String jobSubCategoryName;
    private String jobSubSubCategory;
    private String jobSubSubCategoryName;
    private String industry;
    private String city;
    private String country;
    private String completeAddress;

    public JobApplicationFullDTO(
            Long applicationId, Long candidateProfileId, Long companyProfileId, Long jobPostId,
            String jobTitle, String industry, String jobLocation, String companyName, String companyLogoUrl,
            Integer statusId, Timestamp appliedDate, Timestamp updatedDate,
            String candidateName, String designation, Long currentSalary, String candidateLocation, String skills,
            String resumeKey, String jobCategory, String jobCategoryName,
            String jobSubCategory, String jobSubCategoryName,
            String jobSubSubCategory, String jobSubSubCategoryName,
            String completeAddress
    ) {
        super();
        this.setApplicationId(applicationId);
        this.setCandidateProfileId(candidateProfileId);
        this.setCompanyProfileId(companyProfileId);
        this.setJobPostId(jobPostId);
        this.setJobTitle(jobTitle);
        this.setIndustry(industry);
        this.setJobLocation(jobLocation);
        this.setCompanyName(companyName);
        this.setCompanyLogoUrl(companyLogoUrl);
        this.setSkills(skills);
        this.setResumeKey(resumeKey);
        this.setStatusId(statusId);
        this.setAppliedDate(appliedDate);
        this.setUpdatedDate(updatedDate);
        this.setJobCategory(jobCategory);
        this.setJobCategoryName(jobCategoryName);
        this.setJobSubCategory(jobSubCategory);
        this.setJobSubCategoryName(jobSubCategoryName);
        this.setJobSubSubCategory(jobSubSubCategory);
        this.setJobSubSubCategoryName(jobSubSubCategoryName);
        this.setCompleteAddress(completeAddress);

        this.candidateName = candidateName;
        this.designation = designation;
        this.currentSalary = currentSalary;
        this.candidateLocation = candidateLocation;
    }
}
