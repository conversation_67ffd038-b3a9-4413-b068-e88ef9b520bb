package com.job.jobportal.service;

import com.job.jobportal.dto.BlogDTO;
import com.job.jobportal.model.BlogTopic;
import com.job.jobportal.model.BlogWebsite;
import com.job.jobportal.repository.BlogRepo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.sql.Timestamp;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;

@Service
public class BlogService {

    @Autowired
    BlogRepo blogRepo;

    @Autowired
    S3Service s3Service;

    private static final Logger logger = LoggerFactory.getLogger(BlogService.class);

    public Map<String, Object> addThumbnail(MultipartFile mufile) throws Exception {


        try {

            String url = s3Service.addObject(mufile);
            Map<String, Object> map = new HashMap<>();
            map.put("key", mufile.getOriginalFilename());
            map.put("url", url);
            return map;

        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }


    }

    public BlogWebsite addBlog(BlogDTO blogDTO) {
        try {
            BlogWebsite blogwebsite = new BlogWebsite();
            blogwebsite.setBlogCreatedDate(new Timestamp(Calendar.getInstance().getTimeInMillis()));
            blogwebsite.setBlogTitle(blogDTO.getBlogTitle());
            blogwebsite.setBlogDescription(blogDTO.getBlogDescription());
            blogwebsite.setBlogUrl(blogDTO.getBlogUrl());
            blogwebsite.setFeaturedImage(blogDTO.getFeaturedImage());
            blogwebsite.setThumbnailImage(blogDTO.getThumbnailImage());
            blogwebsite.setFeaturedImageKey(blogDTO.getFeaturedImageKey());
            blogwebsite.setThumbnailImageKey(blogDTO.getThumbnailImageKey());
            blogwebsite.setMetaDescription(blogDTO.getMetaDescription());
            blogwebsite.setMetaKeyWord(blogDTO.getMetaKeyWord());
            blogwebsite.setBlogTags(blogDTO.getBlogTags());
            return blogRepo.save(blogwebsite);
        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }

    public BlogWebsite updateBlog(BlogDTO blogDTO) {
        try {
            BlogWebsite blogwebsite = new BlogWebsite();
            blogwebsite.setBlogId(blogDTO.getBlogId());
            blogwebsite.setBlogTitle(blogDTO.getBlogTitle());
            blogwebsite.setBlogDescription(blogDTO.getBlogDescription());
            blogwebsite.setBlogUrl(blogDTO.getBlogUrl());
            blogwebsite.setFeaturedImage(blogDTO.getFeaturedImage());
            blogwebsite.setThumbnailImage(blogDTO.getThumbnailImage());
            blogwebsite.setFeaturedImageKey(blogDTO.getFeaturedImageKey());
            blogwebsite.setThumbnailImageKey(blogDTO.getThumbnailImageKey());
            blogwebsite.setMetaDescription(blogDTO.getMetaDescription());
            blogwebsite.setMetaKeyWord(blogDTO.getMetaKeyWord());
            blogwebsite.setBlogTags(blogDTO.getBlogTags());
            blogwebsite.setBlogCreatedDate(new Timestamp(Calendar.getInstance().getTimeInMillis()));
            return blogRepo.save(blogwebsite);
        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }

    public void deleteBlog(Long blogId) throws Exception {
        try {
            BlogWebsite blogwebsite = blogRepo.findById(blogId).orElseThrow(() -> new Exception(" Template not present"));
            s3Service.deleteObject(blogwebsite.getFeaturedImageKey());
            s3Service.deleteObject(blogwebsite.getThumbnailImageKey());
            blogRepo.deleteById(blogId);
        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }

    public Map<String, Object> getAllBlog(int page, int size, String blogTitle) {
        try {
            Pageable pagingSort = PageRequest.of(page, size);
            Page<BlogTopic> blogWebsites = blogRepo.findAllBlogTopic(blogTitle, pagingSort);

            Map<String, Object> map = new HashMap<>();
            map.put("blog", blogWebsites.getContent());
            map.put("currentPage", blogWebsites.getNumber());
            map.put("totalItems", blogWebsites.getTotalElements());
            map.put("totalPages", blogWebsites.getTotalPages());
            return map;

        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }

    public Map<String,Object> getBlog(Long blogId) throws Exception {
        BlogWebsite blogWebsite= blogRepo.findById(blogId).orElseThrow(() -> new Exception(" Blog not present"));
        Map<String,Object> result=new HashMap<>();
        result.put("currentBlog",blogWebsite);
        result.put("previousBlog",getPreviousBlogId(blogId));
        result.put("nextBlog",getNextBlogId(blogId));
        return result;
    }

    public Long getPreviousBlogId(Long blogId) {
        Page<Long> result = blogRepo.findPreviousBlogId(blogId, PageRequest.of(0, 1));
        return result.hasContent() ? result.getContent().get(0) : null;
    }

    public Long getNextBlogId(Long blogId) {
        Page<Long> result = blogRepo.findNextBlogId(blogId, PageRequest.of(0, 1));
        return result.hasContent() ? result.getContent().get(0) : null;
    }
}
