package com.job.jobportal.service;

import com.job.jobportal.model.Registereduser;
import com.job.jobportal.repository.RegisteruserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.stream.Collectors;

@Service
public class LoginTrackingService {
    private static final Logger logger = LoggerFactory.getLogger(LoginTrackingService.class);

    @Autowired
    private EmailService emailService;

    @Autowired
    private RegisteruserRepository registeruserRepository;

    @Value("${email.domain.username}")
    private String superAdminEmail;
/*
    @Value("${application.to.superadmin.email}")
    private String toSuperAdminEmail;
*/
    @Async
    public void checkFirstTimeEmployerLogin(Registereduser user) {
        try {
            Set<String> roles = user.getRoles().stream()
                    .map(role -> role.getRolename())
                    .collect(Collectors.toSet());

            if (roles.contains("RECRUITER")) {
                if (user.getHasPassword() == 0) {
                    user.setHasPassword(1);
                    registeruserRepository.save(user);
                    
                    sendEmployerLoginNotification(user);
                }
            }
        } catch (Exception e) {
            logger.error("Error checking first-time employer login: " + e.getMessage(), e);
        }
    }

    @Async
    public void sendEmployerLoginNotification(Registereduser employer) {
        try {
            String subject = "New Employer Login Notification";
            
            String body = "Dear Admin,\n\n" +
                    "A new employer has logged in to the system for the first time.\n\n" +
                    "Employer Details:\n" +
                    "Name: " + employer.getFirstname() + " " + employer.getLastname() + "\n" +
                    "Email: " + employer.getEmail() + "\n" +
                    "Phone: " + employer.getMobileno() + "\n\n" +
                    "This is an automated notification.\n\n" +
                    "Regards,\n" +
                    "Job Portal System";
            
            emailService.sendEmail(superAdminEmail, superAdminEmail, subject, body);
            //emailService.sendEmail(superAdminEmail, toSuperAdminEmail, subject, body);
            
            logger.info("Sent employer login notification for: " + employer.getEmail());
        } catch (Exception e) {
            logger.error("Error sending employer login notification: " + e.getMessage(), e);
        }
    }
}
