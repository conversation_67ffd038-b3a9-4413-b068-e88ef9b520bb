package com.job.jobportal.service;

import com.job.jobportal.dto.SurveyDTO;
import com.job.jobportal.model.Survey;
import com.job.jobportal.repository.SurveyRepo;
import com.job.jobportal.security.UserPrincipal;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

@Service
public class SurveyService {

    @Autowired
    SurveyRepo surveyRepo;

    private static final Logger logger = LoggerFactory.getLogger(SurveyService.class);


    public Survey updateSurvey(SurveyDTO surveyDTO) {
        try {
            Survey survey = new Survey();
            UserPrincipal p = (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

            survey.setUserId(p.getId());
            survey.setSurveyTags(surveyDTO.getSurveyTags());
            return surveyRepo.save(survey);


        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }
}
