package com.job.jobportal.security;


import com.job.jobportal.util.ConstantsUtil;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import io.jsonwebtoken.security.SignatureException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service
public class TokenProvider {

    private static final Logger logger = LoggerFactory.getLogger(TokenProvider.class);

    private final AppProperties appProperties;

    public TokenProvider(AppProperties appProperties) {
        this.appProperties = appProperties;
    }

    public String createToken(Authentication authentication) {
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        SecretKey secretKey = Keys.hmacShaKeyFor(appProperties.getAuth().getTokenSecret().getBytes());
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + appProperties.getAuth().getTokenExpirationMsec());
        Map<String, Object> map = new HashMap<>();
        map.put("role", userPrincipal.getRoles().stream().findFirst().get().getRolename());
        map.put("subscriptionPlanType", userPrincipal.getSubscriptionPlanType());
        map.put("subscriptionStatus", userPrincipal.getSubscriptionStatus());
        map.put("userType", userPrincipal.getClientType());
        map.put("hasPassword", userPrincipal.getHasPassword());

        if (userPrincipal.getRoles().stream().findFirst().get().getRolename().equalsIgnoreCase("RECRUITER")) {
            String permissions = userPrincipal.getPermissions() != null ?
                userPrincipal.getPermissions() :
                ConstantsUtil.DEFAULT_FREE_PLAN_PERMISSIONS;
            map.put("permissions", permissions);

            map.put("jobPostsLimit", userPrincipal.getJobPostsLimit());
            map.put("jobPostsRemaining", userPrincipal.getJobPostsRemaining());
            if (userPrincipal.getJobPostsResetDate() != null) {
                map.put("jobPostsResetDate", userPrincipal.getJobPostsResetDate().getTime());
            }
        }

        return Jwts.builder()
                .subject(Long.toString(userPrincipal.getId()))
                .issuedAt(new Date())
                .expiration(expiryDate).claims(map)
                .signWith(secretKey, Jwts.SIG.HS512)
                .compact();
    }

    public String createToken(UserPrincipal userPrincipal) {
        Date now = new Date();
        SecretKey secretKey = Keys.hmacShaKeyFor(appProperties.getAuth().getTokenSecret().getBytes());
        Date expiryDate = new Date(now.getTime() + appProperties.getAuth().getTokenExpirationMsec());
        Map<String, Object> map = new HashMap<>();
        map.put("role", userPrincipal.getRoles().stream().findFirst().get().getRolename());
        map.put("subscriptionPlanType", userPrincipal.getSubscriptionPlanType());
        map.put("subscriptionStatus", userPrincipal.getSubscriptionStatus());
        map.put("userType", userPrincipal.getClientType());
        map.put("hasCompanyProfile", userPrincipal.getHasCompanyProfileId());
        if(userPrincipal.getRoles().stream().findFirst().get().getRolename().equalsIgnoreCase("CANDIDATE")){
            map.put("hasCandidateProfile", userPrincipal.getHasCandidateProfile());
        }
        map.put("hasPassword", userPrincipal.getHasPassword());

        if (userPrincipal.getRoles().stream().findFirst().get().getRolename().equalsIgnoreCase("RECRUITER")) {
            String permissions = userPrincipal.getPermissions() != null ?
                userPrincipal.getPermissions() :
                ConstantsUtil.DEFAULT_FREE_PLAN_PERMISSIONS;
            map.put("permissions", permissions);

            map.put("jobPostsLimit", userPrincipal.getJobPostsLimit());
            map.put("jobPostsRemaining", userPrincipal.getJobPostsRemaining());
            if (userPrincipal.getJobPostsResetDate() != null) {
                map.put("jobPostsResetDate", userPrincipal.getJobPostsResetDate().getTime());
            }
        }

        return Jwts.builder()
                .subject(Long.toString(userPrincipal.getId()))
                .issuedAt(new Date())
                .expiration(expiryDate).claims(map)
                .signWith(secretKey, Jwts.SIG.HS512)
                .compact();
    }

    public Long getUserIdFromToken(String token) {

        SecretKey secretKey = Keys.hmacShaKeyFor(appProperties.getAuth().getTokenSecret().getBytes());
        Claims claims = Jwts.parser()
                .verifyWith(secretKey).build()
                .parseSignedClaims(token)
                .getPayload();

        return Long.parseLong(claims.getSubject());
    }

    public String getRoleFromToken(String token) {
        SecretKey secretKey = Keys.hmacShaKeyFor(appProperties.getAuth().getTokenSecret().getBytes());

        Claims claims = Jwts.parser()
                .verifyWith(secretKey).build()
                .parseSignedClaims(token)
                .getPayload();

        return claims.get("role", String.class);
    }

    public boolean getHasCompanyProfileFromToken(String token) {
        SecretKey secretKey = Keys.hmacShaKeyFor(appProperties.getAuth().getTokenSecret().getBytes());

            Claims claims = Jwts.parser()
                    .verifyWith(secretKey).build()
                    .parseSignedClaims(token)
                    .getPayload();
            Boolean hasCompanyProfile = claims.get("hasCompanyProfile", Boolean.class);
            return hasCompanyProfile != null ? hasCompanyProfile : false;
    }

    public boolean getHasCandidateProfileFromToken(String token) {
        SecretKey secretKey = Keys.hmacShaKeyFor(appProperties.getAuth().getTokenSecret().getBytes());

        Claims claims = Jwts.parser()
                .verifyWith(secretKey).build()
                .parseSignedClaims(token)
                .getPayload();
        Boolean hasCandidateProfile = claims.get("hasCandidateProfile", Boolean.class);
        return hasCandidateProfile != null ? hasCandidateProfile : false;
    }


    public boolean validateToken(String authToken) {
        try {
            SecretKey secretKey = Keys.hmacShaKeyFor(appProperties.getAuth().getTokenSecret().getBytes());

            Jwts.parser().verifyWith(secretKey).build().parseSignedClaims(authToken);
            return true;
        } catch (SignatureException ex) {
            logger.error("Invalid JWT signature");
        } catch (MalformedJwtException ex) {
            logger.error("Invalid JWT token");
        } catch (ExpiredJwtException ex) {
            logger.error("Expired JWT token");
        } catch (UnsupportedJwtException ex) {
            logger.error("Unsupported JWT token");
        } catch (IllegalArgumentException ex) {
            logger.error("JWT claims string is empty.");
        }
        return false;
    }

    public int getJobPostsLimitFromToken(String token) {
        SecretKey secretKey = Keys.hmacShaKeyFor(appProperties.getAuth().getTokenSecret().getBytes());
        Claims claims = Jwts.parser()
                .verifyWith(secretKey).build()
                .parseSignedClaims(token)
                .getPayload();
        Integer jobPostsLimit = claims.get("jobPostsLimit", Integer.class);
        return jobPostsLimit != null ? jobPostsLimit : 0;
    }

    public int getJobPostsRemainingFromToken(String token) {
        SecretKey secretKey = Keys.hmacShaKeyFor(appProperties.getAuth().getTokenSecret().getBytes());
        Claims claims = Jwts.parser()
                .verifyWith(secretKey).build()
                .parseSignedClaims(token)
                .getPayload();
        Integer jobPostsRemaining = claims.get("jobPostsRemaining", Integer.class);
        return jobPostsRemaining != null ? jobPostsRemaining : 0;
    }

    public Date getJobPostsResetDateFromToken(String token) {
        SecretKey secretKey = Keys.hmacShaKeyFor(appProperties.getAuth().getTokenSecret().getBytes());
        Claims claims = Jwts.parser()
                .verifyWith(secretKey).build()
                .parseSignedClaims(token)
                .getPayload();
        Long resetDateMillis = claims.get("jobPostsResetDate", Long.class);
        return resetDateMillis != null ? new Date(resetDateMillis) : null;
    }

}
