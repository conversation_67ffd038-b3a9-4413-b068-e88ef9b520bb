package com.job.jobportal.service;


import com.job.jobportal.dto.RatingAndReviewDTO;
import com.job.jobportal.model.RatingAndReview;
import com.job.jobportal.repository.RatingAndReviewRepo;
import com.job.jobportal.security.UserPrincipal;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RatingAndReviewService {

    @Autowired
    RatingAndReviewRepo ratingAndReviewRepo;

    private static final Logger logger = LoggerFactory.getLogger(RatingAndReviewService.class);


    public List<RatingAndReview> getAllRating() {
        try {
            return ratingAndReviewRepo.findAll();

        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }

    public void deleteRating(Long ratingAndReviewid) {
        ratingAndReviewRepo.deleteById(ratingAndReviewid);
    }

    public List<RatingAndReview> getAllRating(Long courseId) {
        try {
            return ratingAndReviewRepo.findAllByCourseId(courseId);

        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }

    public RatingAndReview addRating(RatingAndReviewDTO ratingAndReviewDTO) {
        try {
            RatingAndReview ratingAndReview = new RatingAndReview();
            ratingAndReview.setReview(ratingAndReviewDTO.getReview());
            UserPrincipal p = (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            ratingAndReview.setReviewBy(p.getId());
            ratingAndReview.setCourseId(ratingAndReviewDTO.getCourseId());
            ratingAndReview.setRating(ratingAndReviewDTO.getRating());
            //updating value in course
//        Course c=courseRepo.findById(ratingAndReviewDTO.getCourseId()).get();
//        double rating=c.getRating();
//
//        int ratingCount=c.getRatingCount();
//        double temp=(ratingCount*rating)+ratingAndReviewDTO.getRating();
//        ratingCount++;
//        rating=temp/ratingCount;
//        courseRepo.updateCourseRating(rating,ratingCount,ratingAndReviewDTO.getCourseId());

            return ratingAndReviewRepo.save(ratingAndReview);

        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }


}
