package com.job.jobportal.dto;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BulkEmailRequestDTO {

    @NotEmpty(message = "msg.applicant_ids_required")
    private List<Long> applicantIds = new ArrayList<>();

    @NotNull(message = "msg.email_subject_required")
    private String subject;

    @NotNull(message = "msg.email_body_required")
    @JdbcTypeCode(SqlTypes.JSON)
    private JsonNode body;

    @JdbcTypeCode(SqlTypes.JSON)
    private JsonNode signature;
}
