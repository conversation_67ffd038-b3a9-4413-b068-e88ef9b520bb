package com.job.jobportal.repository;

import com.job.jobportal.model.RatingAndReview;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Transactional
public interface RatingAndReviewRepo extends JpaRepository<RatingAndReview, Long> {

    List<RatingAndReview> findAllByCourseId(Long courseId);
}
