package com.job.jobportal.service;

import com.job.jobportal.dto.BulkSeoGenerateRequest;
import com.job.jobportal.dto.BulkSeoGenerateResponse;
import com.job.jobportal.dto.SeoGenerateRequest;
import com.job.jobportal.dto.SeoGenerateResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class SeoGenerateService {
    private static final Logger logger = LoggerFactory.getLogger(SeoGenerateService.class);

    @Autowired
    private MessageSource messageSource;

    public SeoGenerateResponse generateSeoMetadata(SeoGenerateRequest request) {
        try {
            logger.info("Generating SEO metadata for job title: {}, category: {}, subcategory: {}, city: {}",
                    request.getJobTitle(), request.getCategory(), request.getSubcategory(), request.getCity());

            validateRequest(request);

            SeoGenerateResponse response = new SeoGenerateResponse();
            response.setSeoUrl(generateSeoUrl(request));
            response.setMetaTitle(generateMetaTitle(request));
            response.setMetaDescription(generateMetaDescription(request));

            logger.info("Successfully generated SEO metadata: url={}", response.getSeoUrl());
            return response;
        } catch (IllegalArgumentException e) {
            logger.warn("Validation error generating SEO metadata: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("Error generating SEO metadata: {}", e.getMessage(), e);
            throw new RuntimeException("Error generating SEO metadata: " + e.getMessage(), e);
        }
    }

    public BulkSeoGenerateResponse generateBulkSeoMetadata(BulkSeoGenerateRequest bulkRequest) {
        logger.info("Processing bulk SEO request with {} items", bulkRequest.getRequests().size());

        List<SeoGenerateResponse> responses = new ArrayList<>();
        int successCount = 0;

        for (SeoGenerateRequest request : bulkRequest.getRequests()) {
            try {
                if (isInvalidRequest(request)) {
                    logger.warn("Skipping request with missing required fields: category={}, subcategory={}, jobTitle={}, city={}",
                            request.getCategory(), request.getSubcategory(), request.getJobTitle(), request.getCity());
                    responses.add(createDefaultResponse());
                    continue;
                }

                SeoGenerateResponse response = generateSeoMetadata(request);
                responses.add(response);
                successCount++;
            } catch (Exception e) {
                logger.error("Error generating SEO metadata for request: {}", e.getMessage());
                responses.add(createDefaultResponse());
            }
        }

        BulkSeoGenerateResponse bulkResponse = new BulkSeoGenerateResponse();
        bulkResponse.setResponses(responses);
        bulkResponse.setTotalProcessed(bulkRequest.getRequests().size());
        bulkResponse.setSuccessCount(successCount);

        logger.info("Completed bulk SEO metadata generation: total={}, success={}",
                bulkRequest.getRequests().size(), successCount);

        return bulkResponse;
    }

    private boolean isInvalidRequest(SeoGenerateRequest request) {
        return request.getCategory() == null || request.getCategory().trim().isEmpty() ||
               request.getJobTitle() == null || request.getJobTitle().trim().isEmpty() ||
               request.getCity() == null || request.getCity().trim().isEmpty();
    }

    private SeoGenerateResponse createDefaultResponse() {
        SeoGenerateResponse response = new SeoGenerateResponse();
        response.setSeoUrl("/jobs");
        response.setMetaTitle("Job Listing");
        response.setMetaDescription("Browse healthcare job opportunities and find your next career move. Apply today.");
        return response;
    }

    private void validateRequest(SeoGenerateRequest request) {
        if (request.getCategory() == null || request.getCategory().trim().isEmpty()) {
            throw new IllegalArgumentException(
                messageSource.getMessage("msg.category_required", null, "Category is required", LocaleContextHolder.getLocale())
            );
        }

        if (request.getJobTitle() == null || request.getJobTitle().trim().isEmpty()) {
            throw new IllegalArgumentException(
                messageSource.getMessage("msg.job_title_required", null, "Job title is required", LocaleContextHolder.getLocale())
            );
        }

        if (request.getCity() == null || request.getCity().trim().isEmpty()) {
            throw new IllegalArgumentException(
                messageSource.getMessage("msg.city_required", null, "City is required", LocaleContextHolder.getLocale())
            );
        }
    }

    private String generateSeoUrl(SeoGenerateRequest request) {
        StringBuilder urlBuilder = new StringBuilder("/jobs");

        String category = request.getCategory();
        String subcategory = request.getSubcategory();
        String jobTitle = request.getJobTitle();
        String city = request.getCity();
        String district = request.getDistrict();

        if (category == null) category = "";
        if (subcategory == null) subcategory = "";
        if (jobTitle == null) jobTitle = "";
        if (city == null) city = "";
        if (district == null) district = "";

        if (!category.isEmpty()) {
            urlBuilder.append("/").append(slugify(category));
        }

        if (!subcategory.isEmpty()) {
            urlBuilder.append("/").append(slugify(subcategory));
        }

        if (!jobTitle.isEmpty()) {
            urlBuilder.append("/").append(slugify(jobTitle));
        }

        if (!city.isEmpty()) {
            urlBuilder.append("/").append(slugify(city));
        }

        if (!district.isEmpty()) {
            urlBuilder.append("/").append(slugify(district));
        }

        return urlBuilder.toString();
    }

    private String generateMetaTitle(SeoGenerateRequest request) {
        StringBuilder titleBuilder = new StringBuilder();
        String category = request.getCategory();
        String subcategory = request.getSubcategory();
        String jobTitle = request.getJobTitle();
        String city = request.getCity();
        String district = request.getDistrict();

        if (category == null) category = "";
        if (subcategory == null) subcategory = "";
        if (jobTitle == null) jobTitle = "";
        if (city == null) city = "";
        if (district == null) district = "";

        if (!jobTitle.isEmpty()) {
            titleBuilder.append(jobTitle);

            if (!subcategory.isEmpty()) {
                titleBuilder.append(" in ");
                titleBuilder.append(subcategory);
            }
        } else if (!subcategory.isEmpty()) {
            titleBuilder.append(subcategory);
        }

        if (!category.isEmpty()) {
            if (!titleBuilder.isEmpty()) {
                titleBuilder.append(" - ");
            }
            titleBuilder.append(category);
            titleBuilder.append(" Jobs");

            if (!city.isEmpty()) {
                titleBuilder.append(" in ");
                titleBuilder.append(city);

                if (!district.isEmpty()) {
                    titleBuilder.append(", ").append(district);
                }
            }
        }

        if (titleBuilder.isEmpty()) {
            titleBuilder.append("Job Listing");
        }

        String title = titleBuilder.toString();
        if (title.length() > 65) {
            title = title.substring(0, 62) + "...";
        }

        return title;
    }

    private String generateMetaDescription(SeoGenerateRequest request) {
        StringBuilder descBuilder = new StringBuilder();
        String category = request.getCategory();
        String subcategory = request.getSubcategory();
        String jobTitle = request.getJobTitle();
        String city = request.getCity();
        String district = request.getDistrict();

        if (category == null) category = "";
        if (subcategory == null) subcategory = "";
        if (jobTitle == null) jobTitle = "";
        if (city == null) city = "";
        if (district == null) district = "";

        if (!jobTitle.isEmpty()) {
            descBuilder.append("Find the best ");
            descBuilder.append(jobTitle);

            if (!subcategory.isEmpty()) {
                descBuilder.append(" jobs in ");
                descBuilder.append(subcategory);
            } else {
                descBuilder.append(" jobs");
            }

            if (!city.isEmpty()) {
                descBuilder.append(" in ");
                descBuilder.append(city);

                if (!district.isEmpty()) {
                    descBuilder.append(", ").append(district);
                }
            }

            if (!category.isEmpty()) {
                descBuilder.append(". Apply now for ");
                descBuilder.append(category);
                descBuilder.append(" positions and start your career in healthcare.");
            } else {
                descBuilder.append(". Apply now and start your career in healthcare.");
            }
        } else {
            if (!category.isEmpty()) {
                descBuilder.append("Browse ");
                descBuilder.append(category);
                descBuilder.append(" jobs");

                if (!subcategory.isEmpty()) {
                    descBuilder.append(" in the ");
                    descBuilder.append(subcategory);
                    descBuilder.append(" field");
                }

                if (!city.isEmpty()) {
                    descBuilder.append(" in ");
                    descBuilder.append(city);

                    if (!district.isEmpty()) {
                        descBuilder.append(", ").append(district);
                    }
                }

                descBuilder.append(". Find your next career opportunity in healthcare.");
            } else {
                descBuilder.append("Find healthcare job opportunities");

                if (!city.isEmpty()) {
                    descBuilder.append(" in ");
                    descBuilder.append(city);

                    if (!district.isEmpty()) {
                        descBuilder.append(", ").append(district);
                    }
                }

                descBuilder.append(". Browse our listings and apply today.");
            }
        }

        if (descBuilder.isEmpty()) {
            descBuilder.append("Browse healthcare job opportunities and find your next career move. Apply today.");
        }

        String description = descBuilder.toString();
        if (description.length() > 160) {
            description = description.substring(0, 157) + "...";
        }

        return description;
    }

    private String slugify(String input) {
        if (input == null || input.trim().isEmpty()) {
            return "";
        }

        try {
            String slug = input.toLowerCase()
                    .replaceAll("[^a-z0-9\\s-]", "")
                    .replaceAll("\\s+", "-")
                    .replaceAll("-+", "-")
                    .replaceAll("^-|-$", "");

            if (slug.isEmpty()) {
                return "item";
            }

            return slug;
        } catch (Exception e) {
            logger.error("Error slugifying string '{}': {}", input, e.getMessage());
            return "item";
        }
    }
}
