package com.job.jobportal.controller;

import com.job.jobportal.dto.InternalNotificationDTO;
import com.job.jobportal.model.InternalNotification;
import com.job.jobportal.response.ApiResponse;
import com.job.jobportal.response.BadRequestException;
import com.job.jobportal.service.InternalNotificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
public class InternalNotificationController {

    @Autowired
    InternalNotificationService internalNotificationService;

    private static final Logger logger = LoggerFactory.getLogger(InternalNotificationController.class);

    @GetMapping("/internal/notification")
    public ResponseEntity<?> getAllNotification() {

        try {
            List<InternalNotification> notification = internalNotificationService.getAllNotification();
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, notification,
                    "msg.notification_get_all_success"), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);

        }

    }

    @PostMapping("/internal/notification")
    public ResponseEntity<?> addNotification(@RequestBody InternalNotificationDTO internalNotificationDTO) {

        try {
            InternalNotification internalNotification = internalNotificationService.addNotification(internalNotificationDTO);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, internalNotification,
                    "msg.notification_added_success"), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);

        }

    }

}
