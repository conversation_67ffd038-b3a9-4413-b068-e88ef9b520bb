package com.job.jobportal.response;


import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;

/**
 * Created by r<PERSON><PERSON><PERSON><PERSON><PERSON> on 02/08/17.
 */
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class LoginRequest {
    @NotBlank
    @Email
    private String email;

    @NotBlank
    private Long otp;

    @NotBlank
    private String role;

 
}
