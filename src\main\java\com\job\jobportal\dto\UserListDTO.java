package com.job.jobportal.dto;

import com.job.jobportal.model.Registereduser;
import com.job.jobportal.model.Roles;
import com.job.jobportal.security.AuthProvider;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;
import java.util.Set;
import java.util.stream.Collectors;

@Getter
@Setter
@NoArgsConstructor
public class UserListDTO {
    private Long userid;
    private String username;
    private String firstname;
    private String lastname;
    private String email;
    private String mobileno;
    private Timestamp createdOn;
    private AuthProvider provider;
    private Set<String> roles;
    private boolean isActive;
    private boolean hasCompanyProfileId;
    private boolean hasCandidateProfile;

    public static UserListDTO fromEntity(Registereduser user) {
        UserListDTO dto = new UserListDTO();
        dto.setUserid(user.getUserid());
        dto.setUsername(user.getUsername());
        dto.setFirstname(user.getFirstname());
        dto.setLastname(user.getLastname());
        dto.setEmail(user.getEmail());
        dto.setMobileno(user.getMobileno());
        dto.setCreatedOn(user.getCreatedOn());
        dto.setProvider(user.getProvider());
        
        if (user.getRoles() != null) {
            dto.setRoles(user.getRoles().stream()
                .map(Roles::getRolename)
                .collect(Collectors.toSet()));
        }
        
        if (user.getAccountDetails() != null) {
            dto.setActive(user.getAccountDetails().getIsActive() == 1);
        }
        
        dto.setHasCompanyProfileId(user.isHasCompanyProfileId());
        dto.setHasCandidateProfile(user.isHasCandidateProfile());
        
        return dto;
    }
}
