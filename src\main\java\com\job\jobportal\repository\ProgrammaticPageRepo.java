package com.job.jobportal.repository;

import com.job.jobportal.model.ProgrammaticPage;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ProgrammaticPageRepo extends JpaRepository<ProgrammaticPage, Long> {

    @Query("SELECT p FROM ProgrammaticPage p WHERE (:title is null OR p.programmaticPageTitle LIKE %:title%) ORDER BY p.programmaticPageCreatedDate DESC")
    Page<ProgrammaticPage> findAllProgrammaticPages(@Param("title") String title, Pageable pageable);

    Optional<ProgrammaticPage> findByProgrammaticPageTitle(String title);

    @Query("SELECT p FROM ProgrammaticPage p WHERE p.programmaticPageUrl = :url")
    Optional<ProgrammaticPage> findByProgrammaticPageUrl(@Param("url") String url);

    @Query("SELECT p FROM ProgrammaticPage p WHERE p.programmaticPageCategoryId = :categoryId AND p.programmaticPageSubcategoryId = :subcategoryId AND p.programmaticPageCity = :city")
    Optional<ProgrammaticPage> findByProgrammaticPageCategoryIdAndProgrammaticPageSubcategoryIdAndProgrammaticPageCity(
            @Param("categoryId") Integer categoryId,
            @Param("subcategoryId") Integer subcategoryId,
            @Param("city") String city);


    @Query("SELECT p FROM ProgrammaticPage p WHERE p.programmaticPageCategoryId IN (SELECT m.masterDataId FROM MasterData m WHERE m.componentType.id = 12 AND LOWER(m.value) = LOWER(:category)) AND p.programmaticPageSubcategoryId IN (SELECT m.masterDataId FROM MasterData m WHERE m.componentType.id = 17 AND LOWER(m.value) LIKE CONCAT(LOWER(:subcategory), '|%')) AND LOWER(p.programmaticPageCity) = LOWER(:city)")
    Optional<ProgrammaticPage> findByCategoryAndSubcategoryAndCity(
            @Param("category") String category,
            @Param("subcategory") String subcategory,
            @Param("city") String city);

    @Query("SELECT p FROM ProgrammaticPage p WHERE " +
           "((:category IS NULL AND p.programmaticPageCategoryId IS NULL) OR " +
           "(:category IS NOT NULL AND p.programmaticPageCategoryId IN (SELECT m.masterDataId FROM MasterData m WHERE m.componentType.id = 12 AND LOWER(m.value) = LOWER(:category)))) AND " +
           "((:subcategory IS NULL AND p.programmaticPageSubcategoryId IS NULL) OR " +
           "(:subcategory IS NOT NULL AND p.programmaticPageSubcategoryId IN (SELECT m.masterDataId FROM MasterData m WHERE m.componentType.id = 17 AND LOWER(m.value) LIKE CONCAT(LOWER(:subcategory), '|%')))) AND " +
           "((:subSubcategory IS NULL AND p.programmaticPageSubSubcategoryId IS NULL) OR " +
           "(:subSubcategory IS NOT NULL AND p.programmaticPageSubSubcategoryId IN (SELECT m.masterDataId FROM MasterData m WHERE m.componentType.id = 23 AND LOWER(m.value) LIKE CONCAT(LOWER(:subSubcategory), '|%')))) AND " +
           "((:city IS NULL AND p.programmaticPageCity IS NULL) OR " +
           "(:city IS NOT NULL AND LOWER(p.programmaticPageCity) = LOWER(:city)))")
    Optional<ProgrammaticPage> findByCategorySubcategorySubSubcategoryAndLocation(
            @Param("category") String category,
            @Param("subcategory") String subcategory,
            @Param("subSubcategory") String subSubcategory,
            @Param("city") String city);

    @Query("SELECT p FROM ProgrammaticPage p WHERE " +
           "((:category IS NULL AND p.programmaticPageCategoryId IS NULL) OR " +
           "(:category IS NOT NULL AND p.programmaticPageCategoryId IN (SELECT m.masterDataId FROM MasterData m WHERE m.componentType.id = 12 AND LOWER(m.value) = LOWER(:category)))) AND " +
           "((:subcategory IS NULL AND p.programmaticPageSubcategoryId IS NULL) OR " +
           "(:subcategory IS NOT NULL AND p.programmaticPageSubcategoryId IN (SELECT m.masterDataId FROM MasterData m WHERE m.componentType.id = 17 AND LOWER(m.value) LIKE CONCAT(LOWER(:subcategory), '|%')))) AND " +
           "((:subSubcategory IS NULL AND p.programmaticPageSubSubcategoryId IS NULL) OR " +
           "(:subSubcategory IS NOT NULL AND p.programmaticPageSubSubcategoryId IN (SELECT m.masterDataId FROM MasterData m WHERE m.componentType.id = 23 AND LOWER(m.value) LIKE CONCAT(LOWER(:subSubcategory), '|%')))) AND " +
           "((:city IS NULL AND p.programmaticPageCity IS NULL) OR " +
           "(:city IS NOT NULL AND LOWER(p.programmaticPageCity) = LOWER(:city)))")
    List<ProgrammaticPage> findAllByCategorySubcategorySubSubcategoryAndLocation(
            @Param("category") String category,
            @Param("subcategory") String subcategory,
            @Param("subSubcategory") String subSubcategory,
            @Param("city") String city);
}
