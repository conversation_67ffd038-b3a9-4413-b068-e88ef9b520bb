package com.job.jobportal.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.sql.Timestamp;

@Entity
@Getter
@Setter
public class JobApplication {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long applicationId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "candidateProfileId", nullable = false)
    private CandidateProfile candidateProfile;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "companyProfileId", nullable = false)
    private CompanyProfile companyProfile;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "jobPostId", nullable = false)
    private JobPost jobPost;

    private String resumeKey;
    private String resumeUrl;
    private String answer;

    @Column(name = "status")
    private String statusIdStr;

    // Status ID from master data (component_type_id=16)
    @Transient
    private Integer statusId;


    public Integer getStatusId() {
        return statusIdStr != null ? Integer.parseInt(statusIdStr) : null;
    }

    public void setStatusId(Integer statusId) {
        this.statusIdStr = statusId != null ? statusId.toString() : null;
    }

    @CreationTimestamp
    private Timestamp appliedDate;

    @UpdateTimestamp
    private Timestamp updatedDate;
}