package com.job.jobportal.repository;

import com.job.jobportal.model.EducationEntry;
import jakarta.data.repository.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface EducationRepository extends JpaRepository<EducationEntry, Long> {
    @Query("SELECT e FROM EducationEntry e WHERE e.resume.resumeId = :resumeId AND e.educationId = :educationId")
    Optional<EducationEntry> findByResumeIdAndEducationId(
            @Param("resumeId") Long resumeId,
            @Param("educationId") Long educationId
    );
}