package com.job.jobportal.service;

import com.job.jobportal.config.AWSSecretManagerService;
import com.job.jobportal.model.Registereduser;
import com.job.jobportal.model.SubscriptionPlan;
import com.job.jobportal.repository.RegisteruserRepository;
import com.job.jobportal.repository.SubscriptionPlanRepo;
import com.job.jobportal.response.BadRequestException;
import com.job.jobportal.security.UserPrincipal;
import com.job.jobportal.util.ConstantsUtil;
import com.stripe.Stripe;
import com.stripe.exception.StripeException;
import com.stripe.model.*;
import com.stripe.model.checkout.Session;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

@Service
public class SubscriptionService {

    @Value("${stripe.api.key}")
    private String stripeApiKey;

    @Value("${stripe.price.standard.monthly}")
    private String standardMonthlyPriceId;

    @Value("${stripe.price.premium.monthly}")
    private String premiumMonthlyPriceId;

    @Value("${stripe.price.enterprise.monthly}")
    private String enterpriseMonthlyPriceId;

    @Value("${stripe.price.standard.yearly}")
    private String standardYearlyPriceId;

    @Value("${stripe.price.premium.yearly}")
    private String premiumYearlyPriceId;

    @Value("${stripe.price.enterprise.yearly}")
    private String enterpriseYearlyPriceId;



    @Value("${application.baseFrontendUrl}")
    private String baseFrontendUrl;

    @Value("${stripe.webhook.signing.currency}")
    private String currency;


    @Value("${application.multiCurrency}")
    private boolean multiCurrency;

    @Value("${application.multiCurrencyList}")
    private List<String> multiCurrencyList;



    @Autowired
    RegisteruserRepository registeruserRepository;

    @Autowired
    SubscriptionPlanRepo subscriptionPlanRepo;

    @Autowired
    AWSSecretManagerService awsSecretManagerService;



    public String addProduct(String productName,String description,String amount,Map<String, BigDecimal> amountList) throws StripeException {
        Map<String, Object> params = new HashMap<>();
        params.put("name", productName);
        params.put("description", description);
        Product product =null;
        try {

            Map<String, Object> pricemap = new HashMap<>();
            pricemap.put("unit_amount", amount);
            pricemap.put("currency", currency);
            if(multiCurrency){


                Map<String, Object> priceParams = new HashMap<>();
                for(String cur:multiCurrencyList){

                    Map<String, Object> currencyOptions = new HashMap<>();
                    currencyOptions.put("unit_amount",amountList.get(cur).multiply(new BigDecimal(100)).toBigInteger().toString());
                    priceParams.put(cur.toLowerCase(), currencyOptions);
                }
                pricemap.put("currency_options", priceParams);

            }


            params.put("default_price_data", pricemap);
            product= Product.create(params);

        } catch (StripeException e) {
            e.printStackTrace();
            throw e;
        }
        return product.getId();

    }

    public boolean updateProduct(String productId,String productName,String description,String amount) throws StripeException {
        Map<String, Object> params = new HashMap<>();
        params.put("name", productName);
        params.put("description", description);

        try {
            Product product =
                    Product.retrieve(productId);




            Map<String, Object> pricemap = new HashMap<>();
            pricemap.put("unit_amount", amount);
            pricemap.put("currency", currency);
            pricemap.put("product", productId);
            Price p=Price.create(pricemap);


            params.put("default_price", p.getId());
            product.update(params);
        } catch (StripeException e) {
            e.printStackTrace();
            throw e;
        }
        return true;
    }

    public boolean deleteProduct(String productId) throws StripeException {
        Product product;
        try {
            product = Product.retrieve(productId);
            Map<String, Object> params = new HashMap<>();
            params.put("active", false);
            product.update(params);
        } catch (StripeException e) {

            e.printStackTrace();
            throw e;
        }
        return true;


    }


//    public BigDecimal getPrice(Long courseId)   {
//        try{
//            Course course= courseRepo.findById(courseId).get();
//            Product product;
//            List<String> expandList = new ArrayList<>();
//            expandList.add("currency_options");
//            Map<String, Object> params = new HashMap<>();
//            params.put("expand", expandList);
//            product = Product.retrieve(course.getProductId());
//            Price price=Price.retrieve(product.getDefaultPrice(),params,null);
//
//            return  price.getUnitAmountDecimal();
//        }
//        catch (StripeException e){
//            e.printStackTrace();
//            return null;
//        }
//        catch (Exception e){
//            e.printStackTrace();
//            throw e;
//        }
//    }


//    public Map<String,Object> addSession(Long courseId,int quantity) throws StripeException {
//        Map<String,Object> url=new HashMap<>();
//        try {
//
//            Course course= courseRepo.findById(courseId).get();
//            Product product;
//            product = Product.retrieve(course.getProductId());
//
//            List<Object> lineItems = new ArrayList<>();
//            Map<String, Object> lineItem1 = new HashMap<>();
//            lineItem1.put("price", product.getDefaultPrice());
//            lineItem1.put("quantity", quantity);
//            lineItems.add(lineItem1);
//            Map<String, Object> params = new HashMap<>();
//            UserPrincipal p=(UserPrincipal)SecurityContextHolder.getContext().getAuthentication().getPrincipal();
//            Registereduser user=registeruserRepository.findByEmail(p.getEmail()).get();
//            params.put("customer",user.getCustomerStripeId());
//
//            params.put(
//                    "success_url",
//                    baseFrontendUrl+"/thankyou?session_id={CHECKOUT_SESSION_ID}&courseId="+courseId
//            );
//            params.put("cancel_url",
//                    baseFrontendUrl+"/transactionfailed"
//
//            );
//            params.put("line_items", lineItems);
//            params.put("mode", "payment");
//            params.put("allow_promotion_codes",true);
//
//            Session session = Session.create(params);
//            url.put("url",session.getUrl()) ;
//
//        }catch (Exception e){
//            e.printStackTrace();
//            throw e;
//        }
//        return url;
//    }

    public Map<String,Object> addSubscription(String planName) throws StripeException {
        Map<String,Object> url=new HashMap<>();
        try {
//			Map<String, Object> planParams = new HashMap<>();
//			planParams.put("limit", 3);
//
//			PlanCollection plans = Plan.list(planParams);
//			String planId=	plans.getData().stream().filter(plan1 -> plan1.getNickname().equalsIgnoreCase(planName)	).findFirst().get().getId();
//
            // Get price ID based on plan name
            String priceId;

            if ("Standard".equalsIgnoreCase(planName)) {
                priceId = standardMonthlyPriceId;
            } else if ("Premium".equalsIgnoreCase(planName)) {
                priceId = premiumMonthlyPriceId;
            } else if ("Enterprise".equalsIgnoreCase(planName)) {
                priceId = enterpriseMonthlyPriceId;
            } else if ("Trial".equalsIgnoreCase(planName)) {
                priceId = "trial_plan";
            } else {
                throw new BadRequestException("Invalid plan name: " + planName);
            }

            List<Object> lineItems = new ArrayList<>();
            Map<String, Object> lineItem1 = new HashMap<>();
            lineItem1.put("price", priceId);
            lineItem1.put("quantity", 1);
            lineItems.add(lineItem1);
            Map<String, Object> params = new HashMap<>();
            UserPrincipal p=(UserPrincipal)SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            Registereduser user=registeruserRepository.findByEmail(p.getEmail()).get();
            params.put("customer",user.getCustomerStripeId());

            params.put(
                    "success_url",
                    baseFrontendUrl+"/thankyou"
            );
            params.put("cancel_url",
                    baseFrontendUrl+"/transactionfailed"

            );
            params.put("line_items", lineItems);
            params.put("mode", "subscription");
            params.put("payment_method_collection","always");
            Session session = Session.create(params);
            url.put("url",session.getUrl()) ;

        }catch (Exception e){
            e.printStackTrace();
            throw e;
        }
        return url;
    }

    public Map<String,Object> customerPortal() throws StripeException {
        Map<String,Object> url = new HashMap<>();
        try {
            Stripe.apiKey = stripeApiKey;

            // Get current user
            UserPrincipal p = (UserPrincipal)SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            Registereduser user = registeruserRepository.findByEmail(p.getEmail())
                .orElseThrow(() -> new BadRequestException("User not found"));

            if (user.getCustomerStripeId() == null || user.getCustomerStripeId().isEmpty()) {
                throw new BadRequestException("No Stripe customer ID found for user");
            }

            // Create customer portal session
            Map<String, Object> params = new HashMap<>();
            params.put("customer", user.getCustomerStripeId());
            params.put("return_url", baseFrontendUrl + "/account");

            com.stripe.model.billingportal.Session session =
                com.stripe.model.billingportal.Session.create(params);

            url.put("url", session.getUrl());

        }catch (Exception e){
            e.printStackTrace();
            throw e;
        }
        return url;
    }

    public Map<String,Object> addPaymentMethod() throws StripeException {
        Map<String,Object> url=new HashMap<>();
        try {

            Map<String, Object> params = new HashMap<>();
            UserPrincipal p=(UserPrincipal)SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            Registereduser user=registeruserRepository.findByEmail(p.getEmail()).get();
            params.put("customer",user.getCustomerStripeId());

            params.put(
                    "success_url",
                    baseFrontendUrl+"/thankyou"
            );
            params.put("cancel_url",
                    baseFrontendUrl+"/transactionfailed"

            );
            List<String> li= new ArrayList<String>();
            li.add("card");
            params.put("payment_method_types",li);
            params.put("mode", "setup");

            Session session = Session.create(params);
            url.put("url",session.getUrl()) ;

        }catch (Exception e){
            e.printStackTrace();
            throw e;
        }
        return url;
    }

    public boolean verifySession(String session_id) throws StripeException {
        boolean flag=false;
        try {
            Session session = Session.retrieve(session_id);

            if(session.getPaymentStatus().equalsIgnoreCase("paid")){
                flag=true;
            }
        }catch (Exception e){
            e.printStackTrace();
            throw e;
        }
        return flag;
    }

    public Customer createCustomer(Registereduser user) throws StripeException {

        try {
            Customer customer = null;

            Map<String, Object> params = new HashMap<>();
            params.put(
                    "email",
                    user.getEmail()
            );
            params.put(
                    "name",
                    user.getFirstname() + " " + user.getLastname()
            );
            params.put(
                    "phone",
                    user.getMobileno()
            );
            customer = Customer.create(params);
            return customer;
        } catch (StripeException e) {
            e.printStackTrace();
            throw e;
        }
    }

    public void addPlan(List<SubscriptionPlan> subscriptionPlanList) {
        for (SubscriptionPlan subscriptionPlan : subscriptionPlanList) {
            // Check if plan with same name already exists
            Optional<SubscriptionPlan> existingPlan = subscriptionPlanRepo.findByPlanName(subscriptionPlan.getPlanName());
            if (existingPlan.isPresent()) {
                // No need to update anything since we only store the plan name
            } else {
                // Save new plan
                subscriptionPlanRepo.save(subscriptionPlan);
            }
        }
    }

    public Map<String,Object> handleSubscriptionLogic(Subscription subscription) throws StripeException {
        try {
            SubscriptionItemCollection subscriptionItemCollection = subscription.getItems();
            List<SubscriptionItem> subscriptionItems = subscriptionItemCollection.getData();
            SubscriptionItem subscriptionItem = subscriptionItems.stream().findFirst().get();
            Plan plan = subscriptionItem.getPlan();
            List<String> expandList = new ArrayList<>();
            expandList.add("subscriptions");

            Map<String, Object> params = new HashMap<>();
            params.put("expand", expandList);
            Customer customer = Customer.retrieve(subscription.getCustomer(), params, null);

            Product product = Product.retrieve(plan.getProduct());

            int subscriptionType = 0;
            switch (product.getName().toLowerCase()) {
                case "basic": {
                    subscriptionType = ConstantsUtil.SUBSCRIPTION_BASIC_PLAN;
                    break;
                }
                case "starter": {
                    subscriptionType = ConstantsUtil.SUBSCRIPTION_STANDARD_PLAN;
                    break;
                }
                case "advance": {
                    subscriptionType = ConstantsUtil.SUBSCRIPTION_PREMIUM_PLAN;
                    break;
                }
            }
            Map<String,Object> map=new HashMap<>();
            map.put("plan",plan.getProduct());
            map.put("subscriptionType",subscriptionType);
            map.put("email",customer.getEmail());
            map.put("status",subscription.getStatus());
            return  map;
        }catch (Exception e){
            e.printStackTrace();
            throw e;
        }
    }

    public void updateUserSubscription(String subscriptionId,String productId,int subscriptionType,int subscriptionActive,String email){

        try{


       //     registeruserRepository.updateSubscription(subscriptionId,productId,subscriptionType,subscriptionActive,email);

        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

//    public Map<String,Object> createPaymentIntend(Long courseId) throws StripeException {
//        Map<String,Object> url=new HashMap<>();
//        try {
//
//            Course course= courseRepo.findById(courseId).get();
//            Product product;
//            product = Product.retrieve(course.getProductId());
//
//
//
////
//            Map<String, Object> automaticPaymentMethods =
//                    new HashMap<>();
//            automaticPaymentMethods.put("enabled", true);
//            Map<String, Object> params = new HashMap<>();
//            Price price=Price.retrieve(product.getDefaultPrice());
//            params.put("amount",price.getUnitAmount() );
//            params.put("currency", price.getCurrency());
//            UserPrincipal p=(UserPrincipal)SecurityContextHolder.getContext().getAuthentication().getPrincipal();
//            Registereduser user=registeruserRepository.findByEmail(p.getEmail()).get();
//            params.put("customer",user.getCustomerStripeId());
//
//            params.put(
//                    "automatic_payment_methods",
//                    automaticPaymentMethods
//            );
//
//            PaymentIntent paymentIntent =
//                    PaymentIntent.create(params);
//            //
//
//            url.put("clientSecret",paymentIntent.getClientSecret()) ;
//            url.put("apiKey",apiKey);
//            url.put("customerId",user.getCustomerStripeId());
//            url.put("customerName",user.getFirstname() + " " + user.getLastname());
//
//        }catch (Exception e){
//            e.printStackTrace();
//            throw e;
//        }
//        return url;
//    }


}
