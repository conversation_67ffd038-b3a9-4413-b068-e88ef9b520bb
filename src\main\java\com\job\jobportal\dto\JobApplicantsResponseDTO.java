package com.job.jobportal.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class JobApplicantsResponseDTO {
    private JobBasicInfoDTO job;
    private List<ApplicantDTO> applicants;
    private SummaryDTO summary;
    private List<JobBasicInfoDTO> jobs;

    public JobApplicantsResponseDTO(JobBasicInfoDTO job, List<ApplicantDTO> applicants, SummaryDTO summary) {
        this.job = job;
        this.applicants = applicants;
        this.summary = summary;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class JobBasicInfoDTO {
        private Long id;
        private String title;

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
        private Timestamp jobOpeningDate;

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
        private Timestamp applicationDeadlineDate;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ApplicantDTO {
        private Long id;
        private String name;
        private String location;
        private Long expectedSalary;
        private Integer statusId;
        private String statusValue;
        private String profilePic;
        private String designation;
        private Long currentSalary;
        private String resumeUrl;
        private String skills;
        private Long candidateProfileId;
        private Integer salaryCurrencyId;
        private String salaryCurrencyName;
        private Integer experienceId;
        private String experienceName;
        private Integer educationId;
        private String educationName;
        private Integer jobTypeId;
        private String jobTypeName;
        private Integer careerLevelId;
        private String careerLevelName;
        private Integer industryId;
        private String industryName;
        private Integer qualificationId;
        private String qualificationName;
        private Integer jobCategoryId;
        private String jobCategoryName;
        private Integer jobSubCategoryId;
        private String jobSubCategoryName;
        private Integer jobSubSubCategoryId;
        private String jobSubSubCategoryName;
        private String question;
        private String answer;
        private String country;
        private String state;
        private Timestamp appliedDate;
        private String timeFilterName;
        private String candidateAddress;
        private String candidateAddressLineOne;
        private String candidateAddressLineTwo;
        private String candidateAddressCity;
        private String candidateAddressCountry;
        private String candidatePincode;
        private String candidateExperience;
        private String candidateExperienceYears;
        private boolean hasCandidateProfile;
        private String candidateCategoryName;
        private String candidateSubCategoryName;
        private String candidateSubSubCategoryName;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SummaryDTO {
        private Long total;
        private Long applied;
        private Long approved;
        private Long rejected;
    }
}
