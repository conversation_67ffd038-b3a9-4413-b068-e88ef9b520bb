package com.job.jobportal.service;

import com.job.jobportal.dto.CustomerSupportDTO;
import com.job.jobportal.dto.FeedbackDTO;
import com.job.jobportal.model.CustomerSupport;
import com.job.jobportal.model.Feedback;
import com.job.jobportal.model.Registereduser;
import com.job.jobportal.repository.CustomerSupportRepo;
import com.job.jobportal.repository.FeedbackRepo;
import com.job.jobportal.repository.RegisteruserRepository;
import com.job.jobportal.util.CommonUtils;
import com.job.jobportal.util.ConstantsUtil;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class HelpAndFeedbackService {

    @Autowired
    ModelMapper modelMapper;

    @Autowired
    EmailService emailService;

    @Autowired
    RegisteruserRepository registeruserRepository;

    @Autowired
    private FeedbackRepo feedbackRepo;

    @Autowired
    private CustomerSupportRepo customerSupportRepo;

    @Value("${application.certificatecode}")
    private String certificateCode;

    @Value("${application.ticketEmail}")
    private String ticketEmail;

    @Value("${application.email}")
    private String email;

    private static final Logger logger = LoggerFactory.getLogger(HelpAndFeedbackService.class);


    public Feedback addFeedback(FeedbackDTO feedbackDTO) {
        Feedback feedback = modelMapper.map(feedbackDTO, Feedback.class);
        feedback.setFeedbackTime(CommonUtils.getCurrentTime());
        return feedbackRepo.save(feedback);
    }

    public List<Feedback> getAllFeedback() {
        return feedbackRepo.findAll();
    }

    public CustomerSupport addCustomerIssue(CustomerSupportDTO customerSupportDTO) {
        CustomerSupport customerSupport = modelMapper.map(customerSupportDTO, CustomerSupport.class);
        Optional<CustomerSupport> lastCustomerSupport = customerSupportRepo.findTopByOrderByCustomerSupportIdDesc();
        String ticketNumber = "";
        if (lastCustomerSupport.isPresent()) {
            ticketNumber = lastCustomerSupport.get().getTicketNumber();
            int nextNumber = Integer.parseInt(ticketNumber.substring(3)) + 1;
            ticketNumber = certificateCode + "-" + nextNumber;
            customerSupport.setTicketNumber(ticketNumber);
        } else {
            ticketNumber = certificateCode + "-" + 1;
            customerSupport.setTicketNumber(ticketNumber);
        }
        customerSupport.setIssueTime(CommonUtils.getCurrentTime());
        Registereduser user = registeruserRepository.findByUsername(CommonUtils.getUserPrincipal().getUsername());
        String issueCategory = "";
        switch (customerSupport.getIssueCategory()) {
            case ConstantsUtil.ISSUE_ORDER_RELATED: {
                issueCategory = "Order Related Query";
                break;
            }
            case ConstantsUtil.ISSUE_DELIVERY_RELATED: {
                issueCategory = "Delivery Related Query";
                break;
            }
            case ConstantsUtil.ISSUE_PAYMENT_RELATED: {
                issueCategory = "Payment Related Query";
                break;
            }
            case ConstantsUtil.ISSUE_STORAGE_RELATED: {
                issueCategory = "Storage Related Query";
                break;
            }
            case ConstantsUtil.ISSUE_OTHERS_RELATED: {
                issueCategory = "Other Related Query";
                break;
            }
            default:
                break;
        }

        String issuePriority = "";
        switch (customerSupport.getIssueCategory()) {
            case ConstantsUtil.ISSUE_P1: {
                issuePriority = "P1 Issue";
                break;
            }
            case ConstantsUtil.ISSUE_P2: {
                issuePriority = "P2 Issue";
                break;
            }
            case ConstantsUtil.ISSUE_P3: {
                issuePriority = "P3 Issue";
                break;
            }

            default:
                break;
        }

        String body = "The following user  raised issue  :\n" + "\n" +
                "First Name - " + user.getFirstname() + "\n" + "\n" +
                "Last Name - " + user.getLastname() + "\n" + "\n" +
                "Email - " + user.getEmail() + "\n" + "\n" +
                "Mobile Number - " + user.getMobileno() + "\n" + "\n"
                + "\n" + "\n" +
                "The Issue Details are  :\n" + "\n" +
                "Ticket Number - #" + ticketNumber + "\n" + "\n" +
                "Ticket Category - " + issueCategory + "\n" + "\n" +
                "Ticket Priority - " + issuePriority + "\n" + "\n" +
                "Issue Description - " + customerSupport.getIssueDescription() + "\n" + "\n";
        CustomerSupport customerSupportSave = customerSupportRepo.save(customerSupport);
        emailService.sendEmail(email, ticketEmail, "Customer Ticket = #" + ticketNumber, body);

        body = "The following user  raised issue  :\n" + "\n" +
                "First Name - " + user.getFirstname() + "\n" + "\n" +
                "Last Name - " + user.getLastname() + "\n" + "\n" +
                "Email - " + user.getEmail() + "\n" + "\n" +
                "Mobile Number - " + user.getMobileno() + "\n" + "\n"
                + "\n" + "\n" +
                "The Issue Details are  :\n" + "\n" +
                "Ticket Number - #" + ticketNumber + "\n" + "\n" +
                "Ticket Category - " + issueCategory + "\n" + "\n" +
                "Ticket Priority - " + issuePriority + "\n" + "\n" +
                "Issue Description - " + customerSupport.getIssueDescription() + "\n" + "\n"
        ;
        emailService.sendEmail(ticketEmail, user.getEmail(), "Customer Ticket = #" + ticketNumber, body);
        return customerSupportSave;
    }
}
