package com.job.jobportal.service;

import com.job.jobportal.dto.UploadResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest;
import software.amazon.awssdk.services.s3.model.ObjectCannedACL;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

import java.io.IOException;
import java.util.UUID;

@Service
public class FileStorageService {

    private static final Logger logger = LoggerFactory.getLogger(FileStorageService.class);

    @Autowired
    private S3Client s3Client;

    @Value("${application.aws.bucketname}")
    private String bucketName;

    public UploadResponse uploadFile(MultipartFile file) throws IOException {
        String objectKey = "uploads/" +file.getOriginalFilename();
        String fullKey = bucketName + "/" + objectKey;

        PutObjectRequest request = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(objectKey)
                .acl(ObjectCannedACL.PUBLIC_READ)
                .contentType(file.getContentType())
                .build();

        s3Client.putObject(request,
                RequestBody.fromInputStream(file.getInputStream(), file.getSize()));

        logger.debug("Uploaded file to S3: {}", fullKey);

        return new UploadResponse(getUrl(objectKey)
                ,
                fullKey
        );
    }

    public void deleteFile(String fullKey) {
        String objectKey = fullKey.replaceFirst(bucketName + "/", "");

        DeleteObjectRequest deleteRequest = DeleteObjectRequest.builder()
                .bucket(bucketName)
                .key(objectKey)
                .build();

        s3Client.deleteObject(deleteRequest);
        logger.info("Deleted file from S3: {}", fullKey);
    }

    private String getUrl(String key) {
        return s3Client.utilities().getUrl(builder -> builder.bucket(bucketName).key(key)).toString();
    }
}