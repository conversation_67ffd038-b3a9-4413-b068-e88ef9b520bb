package com.job.jobportal.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
public class EducationDTO {
    private Long educationId;
    private LocalDate endDate;
    private String description;

    @NotBlank(message = "msg.validation.institution_required")
    private String institution;

    @NotBlank(message = "msg.validation.degree_required")
    private String degree;

    @NotNull(message = "msg.validation.start_date_required")
    private LocalDate startDate;
}