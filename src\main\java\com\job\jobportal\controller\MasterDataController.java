package com.job.jobportal.controller;

import com.job.jobportal.dto.MasterDataDto;
import com.job.jobportal.dto.MasterDataResponseDto;
import com.job.jobportal.response.ApiResponse;
import com.job.jobportal.service.MasterDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Locale;

@RestController
@RequestMapping("/api")
public class MasterDataController {

    @Autowired
    private MasterDataService masterDataService;

    @Autowired
    private MessageSource messageSource;

    private static final Logger logger = LoggerFactory.getLogger(MasterDataController.class);

    @GetMapping("/masterdata")
    public ResponseEntity<?> getMasterData(
            @RequestParam("componentTypeIds") List<Integer> componentTypeIds,
            Locale locale) {
        try {
            MasterDataResponseDto response = masterDataService.getMasterData(componentTypeIds);
            String message = messageSource.getMessage("msg.master_data_fetched", null, locale);
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.OK, true, response, message),
                    HttpStatus.OK
            );
        } catch (RuntimeException e) {
            logger.error("Error fetching master data: {}", e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.BAD_REQUEST, false, null, e.getMessage()),
                    HttpStatus.BAD_REQUEST
            );
        }
    }

    @GetMapping("/job-subcategories/{categoryId}")
    public ResponseEntity<?> getJobSubcategories(
            @PathVariable Integer categoryId,
            Locale locale) {
        try {
            List<MasterDataDto> subcategories = masterDataService.getSubcategoriesForCategory(categoryId);
            String message = messageSource.getMessage("msg.subcategories_fetched", null, locale);
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.OK, true, subcategories, message),
                    HttpStatus.OK
            );
        } catch (Exception e) {
            logger.error("Error fetching subcategories for category ID {}: {}", categoryId, e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.BAD_REQUEST, false, null, e.getMessage()),
                    HttpStatus.BAD_REQUEST
            );
        }
    }

    @GetMapping("/resource-subcategories/{subcategoryId}")
    public ResponseEntity<?> getResourceSubcategories(
            @PathVariable Integer subcategoryId,
            Locale locale) {
        try {
            List<MasterDataDto> subcategories = masterDataService.getResourceSubcategories(subcategoryId);
            String message = messageSource.getMessage("msg.resource_subcategories_fetched", null, locale);
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.OK, true, subcategories, message),
                    HttpStatus.OK
            );
        } catch (Exception e) {
            logger.error("Error fetching resource subcategories for subcategory ID {}: {}", subcategoryId, e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.BAD_REQUEST, false, null, e.getMessage()),
                    HttpStatus.BAD_REQUEST
            );
        }
    }

    @GetMapping("/job-subsubcategories/{subcategoryId}")
    public ResponseEntity<?> getJobSubSubcategories(
            @PathVariable Integer subcategoryId,
            Locale locale) {
        try {
            List<MasterDataDto> subSubcategories = masterDataService.getJobSubSubcategories(subcategoryId);
            String message = messageSource.getMessage("msg.subsubcategories_fetched", null, locale);
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.OK, true, subSubcategories, message),
                    HttpStatus.OK
            );
        } catch (Exception e) {
            logger.error("Error fetching job sub-subcategories for subcategory ID {}: {}", subcategoryId, e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.BAD_REQUEST, false, null, e.getMessage()),
                    HttpStatus.BAD_REQUEST
            );
        }
    }

    @GetMapping("/job-skills")
    public ResponseEntity<?> getJobSkills(Locale locale) {
        try {
            List<MasterDataDto> jobSkills = masterDataService.getJobSkills();
            String message = messageSource.getMessage("msg.job_skills_fetched", null, "Job skills fetched successfully", locale);
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.OK, true, jobSkills, message),
                    HttpStatus.OK
            );
        } catch (Exception e) {
            logger.error("Error fetching job skills: {}", e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.BAD_REQUEST, false, null, e.getMessage()),
                    HttpStatus.BAD_REQUEST
            );
        }
    }

    @GetMapping("/districts")
    public ResponseEntity<?> getDistricts(Locale locale) {
        try {
            List<MasterDataDto> districts = masterDataService.getDistricts();
            String message = messageSource.getMessage("msg.districts_fetched", null, locale);
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.OK, true, districts, message),
                    HttpStatus.OK
            );
        } catch (Exception e) {
            logger.error("Error fetching districts: {}", e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.BAD_REQUEST, false, null, e.getMessage()),
                    HttpStatus.BAD_REQUEST
            );
        }
    }
}