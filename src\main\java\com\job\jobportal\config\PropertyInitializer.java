package com.job.jobportal.config;

import com.amazonaws.services.secretsmanager.AWSSecretsManager;
import com.amazonaws.services.secretsmanager.model.GetSecretValueRequest;
import com.amazonaws.services.secretsmanager.model.GetSecretValueResult;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stripe.Stripe;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

@Component
public class PropertyInitializer implements ApplicationRunner {
    @Autowired
    AWSSecretsManager awsSecretsManager;

    @Autowired
    AWSSecretManagerService awsSecretManagerService;

    @Value("${application.aws.secretName}")
    String secretName;

    @Override
    public void run(ApplicationArguments args) {
        // Modify the property here programmatically
        JsonNode jsonNode;
        GetSecretValueRequest request = new GetSecretValueRequest().withSecretId(secretName);
        GetSecretValueResult result = awsSecretsManager.getSecretValue(request);
        ObjectMapper objectMapper = new ObjectMapper();
        String secretString = result.getSecretString();
        try {
            jsonNode = objectMapper.readTree(secretString);
        } catch (Exception e) {
            throw new RuntimeException("Failed to parse secret value JSON.", e);
        }
        awsSecretManagerService.setStripeKey(jsonNode.get("stripeKey").asText());
        awsSecretManagerService.setStripeSecret(jsonNode.get("stripeSecret").asText());
        awsSecretManagerService.setWebhookSecret(jsonNode.get("webhookSecret").asText());
        awsSecretManagerService.setTwilioAccountId(jsonNode.get("twilioAccountId").asText());
        awsSecretManagerService.setTwilioToken(jsonNode.get("twilioToken").asText());
        Stripe.apiKey = jsonNode.get("stripeSecret").asText();
    }


}
