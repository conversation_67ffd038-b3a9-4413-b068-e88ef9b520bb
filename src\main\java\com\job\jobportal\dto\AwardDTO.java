package com.job.jobportal.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
public class AwardDTO {
    private Long awardId;

    @NotBlank(message = "{msg.validation.award_title_required}")
    private String title;

    @NotBlank(message = "{msg.validation.issuer_required}")
    private String issuer;

    @NotNull(message = "{msg.validation.issue_date_required}")
    private LocalDate issueDate;
    private String description;
}