package com.job.jobportal.service;


import com.job.jobportal.dto.SEOPagesDTO;
import com.job.jobportal.model.MasterData;
import com.job.jobportal.model.SEOPages;
import com.job.jobportal.model.SEOPagesTopic;
import com.job.jobportal.repository.MasterDataRepository;
import com.job.jobportal.repository.SEOPagesRepo;
import com.job.jobportal.util.CommonUtils;
import com.job.jobportal.util.ConstantsUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class SEOPagesService {

    @Autowired
    SEOPagesRepo seoPageRepo;

    @Autowired
    S3Service s3Service;

    @Autowired
    MasterDataRepository masterDataRepository;

    private static final Logger logger = LoggerFactory.getLogger(SEOPagesService.class);


    public SEOPages addSeoPages(SEOPagesDTO seoPagesDTO) {
        try {
            boolean allCategoriesEmpty = (seoPagesDTO.getCategoryId() == null || seoPagesDTO.getCategoryId() == 0) &&
                                        (seoPagesDTO.getSubcategoryId() == null || seoPagesDTO.getSubcategoryId() == 0) &&
                                        (seoPagesDTO.getSubSubcategoryId() == null || seoPagesDTO.getSubSubcategoryId() == 0);

            if (allCategoriesEmpty && seoPagesDTO.getSeoPagesTitle() != null && !seoPagesDTO.getSeoPagesTitle().isEmpty()) {
                String title = seoPagesDTO.getSeoPagesTitle().toLowerCase().trim();
                List<SEOPages> existingPages = seoPageRepo.findAll();

                for (SEOPages page : existingPages) {
                    if (page.getSeoPagesTitle() != null &&
                        page.getSeoPagesTitle().toLowerCase().trim().equals(title)) {
                        logger.error("A SEO page with exact title '{}' already exists and all categories are empty", seoPagesDTO.getSeoPagesTitle());
                        throw new RuntimeException("A SEO page with the same title already exists: " + seoPagesDTO.getSeoPagesTitle());
                    }
                }

                String normalizedTitle = title.replace("-", " ").replace("_", " ");
                for (SEOPages page : existingPages) {
                    if (page.getSeoPagesTitle() != null) {
                        String normalizedPageTitle = page.getSeoPagesTitle().toLowerCase().trim()
                            .replace("-", " ").replace("_", " ");

                        if (normalizedPageTitle.equals(normalizedTitle)) {
                            logger.error("A SEO page with similar title '{}' already exists and all categories are empty", page.getSeoPagesTitle());
                            throw new RuntimeException("A SEO page with a similar title already exists: " + page.getSeoPagesTitle());
                        }
                    }
                }

                for (SEOPages page : existingPages) {
                    if (page.getSeoPagesTitle() != null &&
                        page.getSeoPagesTitle().equalsIgnoreCase(seoPagesDTO.getSeoPagesTitle().trim())) {
                        logger.error("A SEO page with case-insensitive title '{}' already exists and all categories are empty", page.getSeoPagesTitle());
                        throw new RuntimeException("A SEO page with the same title (different case) already exists: " + page.getSeoPagesTitle());
                    }
                }
            }

            String url;
            if (seoPagesDTO.getSeoPagesUrl() == null || seoPagesDTO.getSeoPagesUrl().isEmpty()) {
                String categoryName = getCategoryName(seoPagesDTO.getCategoryId());
                String subcategoryName = getSubcategoryName(seoPagesDTO.getSubcategoryId(), seoPagesDTO.getCategoryId());
                String subSubcategoryName = getSubSubcategoryName(seoPagesDTO.getSubSubcategoryId(), seoPagesDTO.getSubcategoryId());
                String title = seoPagesDTO.getSeoPagesTitle();

                logger.info("URL Generation - CategoryID: {} -> Name: '{}'", seoPagesDTO.getCategoryId(), categoryName);
                logger.info("URL Generation - SubcategoryID: {} -> Name: '{}'", seoPagesDTO.getSubcategoryId(), subcategoryName);
                logger.info("URL Generation - SubSubcategoryID: {} -> Name: '{}'", seoPagesDTO.getSubSubcategoryId(), subSubcategoryName);

                url = generateSeoUrl(categoryName, subcategoryName, subSubcategoryName, seoPagesDTO.getCustomSlug(), title);
                logger.info("Generated URL: {}", url);
            } else {
                url = removeBaseUrl(seoPagesDTO.getSeoPagesUrl());
                logger.info("Using provided URL after removing base URL: {}", url);
            }

            Optional<SEOPages> existingPageWithUrl = findSeoPageByExactUrl(url);
            if (existingPageWithUrl.isPresent()) {
                logger.error("A SEO page with URL '{}' already exists", url);
                throw new RuntimeException("A SEO page with the same URL already exists: " + url);
            }

            SEOPages seoPages = getSeoPages(seoPagesDTO, url);
            return seoPageRepo.save(seoPages);
        } catch (Exception e) {
            logger.error(e.getMessage());
            throw e;
        }
    }

    private static SEOPages getSeoPages(SEOPagesDTO seoPagesDTO, String url) {
        SEOPages seoPages = new SEOPages();
        seoPages.setSeoPagesTitle(seoPagesDTO.getSeoPagesTitle());
        seoPages.setSeoPagesDescription(seoPagesDTO.getSeoPagesDescription());
        seoPages.setCategoryId(seoPagesDTO.getCategoryId());
        seoPages.setSubcategoryId(seoPagesDTO.getSubcategoryId());
        seoPages.setSubSubcategoryId(seoPagesDTO.getSubSubcategoryId());
        seoPages.setCustomSlug(seoPagesDTO.getCustomSlug());
        seoPages.setSeoPagesUrl(url);
        seoPages.setFeaturedImage(seoPagesDTO.getFeaturedImage());
        seoPages.setThumbnailImage(seoPagesDTO.getThumbnailImage());
        seoPages.setFeaturedImageKey(seoPagesDTO.getFeaturedImageKey());
        seoPages.setThumbnailImageKey(seoPagesDTO.getThumbnailImageKey());
        seoPages.setMetaDescription(seoPagesDTO.getMetaDescription());
        seoPages.setMetaKeyWord(seoPagesDTO.getMetaKeyWord());
        seoPages.setSeoPagesTags(seoPagesDTO.getSeoPagesTags());
        seoPages.setSeoPagesCreatedDate(CommonUtils.getCurrentTime());
        return seoPages;
    }

    public Map<String, Object> addThumbnail(MultipartFile mufile) throws Exception {


        try {

            String url = s3Service.addObject(mufile);
            Map<String, Object> map = new HashMap<>();
            map.put("key", mufile.getOriginalFilename());
            map.put("url", url);
            return map;

        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }


    }

    public SEOPages updateSeoPages(SEOPagesDTO seoPagesDTO) {
        try {
            Optional<SEOPages> existingPageOpt = seoPageRepo.findById(seoPagesDTO.getSeoPagesId());
            if (existingPageOpt.isEmpty()) {
                logger.error("SEO page with ID {} not found", seoPagesDTO.getSeoPagesId());
                throw new RuntimeException("SEO page not found with ID: " + seoPagesDTO.getSeoPagesId());
            }

            SEOPages existingPage = existingPageOpt.get();

            boolean allCategoriesEmpty = (seoPagesDTO.getCategoryId() == null || seoPagesDTO.getCategoryId() == 0) &&
                                        (seoPagesDTO.getSubcategoryId() == null || seoPagesDTO.getSubcategoryId() == 0) &&
                                        (seoPagesDTO.getSubSubcategoryId() == null || seoPagesDTO.getSubSubcategoryId() == 0);

            if (allCategoriesEmpty && seoPagesDTO.getSeoPagesTitle() != null &&
                !seoPagesDTO.getSeoPagesTitle().equals(existingPage.getSeoPagesTitle())) {

                String newTitle = seoPagesDTO.getSeoPagesTitle().toLowerCase().trim();
                List<SEOPages> allPages = seoPageRepo.findAll();
                boolean titleConflict = false;

                for (SEOPages page : allPages) {
                    if (!Objects.equals(page.getSeoPagesId(), seoPagesDTO.getSeoPagesId()) &&
                        page.getSeoPagesTitle() != null &&
                        page.getSeoPagesTitle().toLowerCase().trim().equals(newTitle)) {

                        logger.info("A SEO page with exact title '{}' already exists and all categories are empty", seoPagesDTO.getSeoPagesTitle());
                        titleConflict = true;
                        break;
                    }
                }

                if (!titleConflict) {
                    String normalizedTitle = newTitle.replace("-", " ").replace("_", " ");
                    for (SEOPages page : allPages) {
                        if (!Objects.equals(page.getSeoPagesId(), seoPagesDTO.getSeoPagesId()) &&
                            page.getSeoPagesTitle() != null) {

                            String normalizedPageTitle = page.getSeoPagesTitle().toLowerCase().trim()
                                .replace("-", " ").replace("_", " ");

                            if (normalizedPageTitle.equals(normalizedTitle)) {
                                logger.info("A SEO page with normalized title '{}' already exists and all categories are empty", page.getSeoPagesTitle());
                                titleConflict = true;
                                break;
                            }
                        }
                    }
                }

                if (titleConflict) {
                    logger.warn("Title conflict detected when all categories are empty");
                    throw new RuntimeException("A SEO page with the same or similar title already exists: " + seoPagesDTO.getSeoPagesTitle());
                }
            }

            String url;
            if (seoPagesDTO.getSeoPagesUrl() == null || seoPagesDTO.getSeoPagesUrl().isEmpty()) {
                String categoryName = getCategoryName(seoPagesDTO.getCategoryId());
                String subcategoryName = getSubcategoryName(seoPagesDTO.getSubcategoryId(), seoPagesDTO.getCategoryId());
                String subSubcategoryName = getSubSubcategoryName(seoPagesDTO.getSubSubcategoryId(), seoPagesDTO.getSubcategoryId());
                String title = seoPagesDTO.getSeoPagesTitle();

                logger.info("URL Generation (Update) - CategoryID: {} -> Name: '{}'", seoPagesDTO.getCategoryId(), categoryName);
                logger.info("URL Generation (Update) - SubcategoryID: {} -> Name: '{}'", seoPagesDTO.getSubcategoryId(), subcategoryName);
                logger.info("URL Generation (Update) - SubSubcategoryID: {} -> Name: '{}'", seoPagesDTO.getSubSubcategoryId(), subSubcategoryName);

                url = generateSeoUrl(categoryName, subcategoryName, subSubcategoryName, seoPagesDTO.getCustomSlug(), title);
                logger.info("Generated URL (Update): {}", url);
            } else {
                url = removeBaseUrl(seoPagesDTO.getSeoPagesUrl());
                logger.info("Using provided URL after removing base URL (Update): {}", url);
            }

            if (!url.equals(existingPage.getSeoPagesUrl())) {
                Optional<SEOPages> existingPageWithUrl = findSeoPageByExactUrl(url);
                if (existingPageWithUrl.isPresent() && !existingPageWithUrl.get().getSeoPagesId().equals(seoPagesDTO.getSeoPagesId())) {
                    logger.warn("A SEO page with URL '{}' already exists (ID: {})", url, existingPageWithUrl.get().getSeoPagesId());
                    url = url + "-" + seoPagesDTO.getSeoPagesId();
                    logger.info("Generated unique URL to avoid conflict: {}", url);
                }
            }

            SEOPages seoPages = getSeoPages(seoPagesDTO, url, existingPage);

            return seoPageRepo.save(seoPages);
        } catch (Exception e) {
            logger.error(e.getMessage());
            throw e;
        }
    }

    private static SEOPages getSeoPages(SEOPagesDTO seoPagesDTO, String url, SEOPages existingPage) {
        SEOPages seoPages = new SEOPages();
        seoPages.setSeoPagesId(seoPagesDTO.getSeoPagesId());
        seoPages.setSeoPagesTitle(seoPagesDTO.getSeoPagesTitle());
        seoPages.setSeoPagesDescription(seoPagesDTO.getSeoPagesDescription());
        seoPages.setCategoryId(seoPagesDTO.getCategoryId());
        seoPages.setSubcategoryId(seoPagesDTO.getSubcategoryId());
        seoPages.setSubSubcategoryId(seoPagesDTO.getSubSubcategoryId());
        seoPages.setCustomSlug(seoPagesDTO.getCustomSlug());
        seoPages.setSeoPagesUrl(url);
        seoPages.setFeaturedImage(seoPagesDTO.getFeaturedImage());
        seoPages.setThumbnailImage(seoPagesDTO.getThumbnailImage());
        seoPages.setFeaturedImageKey(seoPagesDTO.getFeaturedImageKey());
        seoPages.setThumbnailImageKey(seoPagesDTO.getThumbnailImageKey());
        seoPages.setMetaDescription(seoPagesDTO.getMetaDescription());
        seoPages.setMetaKeyWord(seoPagesDTO.getMetaKeyWord());
        seoPages.setSeoPagesTags(seoPagesDTO.getSeoPagesTags());
        seoPages.setSeoPagesCreatedDate(existingPage.getSeoPagesCreatedDate());
        return seoPages;
    }

    public void deleteSeoPages(Long seoPagesId) {
        try {
            SEOPages seoPages = seoPageRepo.findById(seoPagesId).get();
            s3Service.deleteObject(seoPages.getFeaturedImageKey());
            s3Service.deleteObject(seoPages.getThumbnailImageKey());
            seoPageRepo.deleteById(seoPagesId);
        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }

    public Map<String, Object> getAllSeoPages(int page, int size, String seoPagesTitle) {
        try {
            Pageable pagingSort = PageRequest.of(page, size);
            Page<SEOPagesTopic> seoPagess = seoPageRepo.findAllSeoPagetopic(seoPagesTitle, pagingSort);

            Map<String, Object> map = new HashMap<>();
            map.put("seopages", seoPagess.getContent());
            map.put("currentPage", seoPagess.getNumber());
            map.put("totalItems", seoPagess.getTotalElements());
            map.put("totalPages", seoPagess.getTotalPages());
            return map;

        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }

    public SEOPages getSeoPages(String seoPagesTitle) {
        return seoPageRepo.findBySeoPagesTitle(seoPagesTitle).get();
    }

    public SEOPages getSeoPagesByURL(String seoPagesUrl) {
        return seoPageRepo.findBySeoPagesUrl(seoPagesUrl).get();
    }


    public SEOPages getSeoPagesById(Long seoPagesId) {
        return seoPageRepo.findById(seoPagesId).get();
    }


    private String getCategoryName(Integer categoryId) {
        if (categoryId == null) {
            logger.info("getCategoryName: categoryId is null, returning empty string");
            return "";
        }

        logger.info("getCategoryName: Looking for categoryId {} in component type {}", categoryId, ConstantsUtil.COMPONENT_TYPE_SEO_CATEGORIES);
        List<MasterData> categories = masterDataRepository.findByComponentType_Id(ConstantsUtil.COMPONENT_TYPE_SEO_CATEGORIES);

        List<MasterData> matchingCategories = categories.stream()
            .filter(data -> data.getMasterDataId() == categoryId)
            .toList();

        if (!matchingCategories.isEmpty()) {
            MasterData category = matchingCategories.get(0);
            logger.info("getCategoryName: Found category with value: {}", category.getValue());
            return category.getValue();
        } else {
            logger.info("getCategoryName: No category found for ID {} in component type {}, returning empty string",
                    categoryId, ConstantsUtil.COMPONENT_TYPE_SEO_CATEGORIES);
            return "";
        }
    }


    private String getSubcategoryName(Integer subcategoryId, Integer categoryId) {
        if (subcategoryId == null || categoryId == null) {
            logger.info("getSubcategoryName: subcategoryId or categoryId is null, returning empty string");
            return "";
        }

        logger.info("getSubcategoryName: Looking for subcategoryId {} in component type {}", subcategoryId, ConstantsUtil.COMPONENT_TYPE_SEO_SUBCATEGORIES);
        List<MasterData> subcategories = masterDataRepository.findByComponentType_Id(ConstantsUtil.COMPONENT_TYPE_SEO_SUBCATEGORIES);

        List<MasterData> matchingSubcategories = subcategories.stream()
            .filter(data -> data.getMasterDataId() == subcategoryId)
            .toList();

        logger.info("getSubcategoryName: Found {} entries with masterDataId {}", matchingSubcategories.size(), subcategoryId);

        for (MasterData subcategory : matchingSubcategories) {
            if (subcategory.getValue() != null) {
                String[] parts = subcategory.getValue().split("\\|");
                if (parts.length > 1) {
                    try {
                        int parentId = Integer.parseInt(parts[1]);

                        if (parentId == categoryId) {
                            logger.info("getSubcategoryName: Found subcategory with value: {}, returning part: {}", subcategory.getValue(), parts[0]);
                            return parts[0];
                        } else {
                            logger.info("getSubcategoryName: Parent mismatch - subcategory {} has parent ID {} which does not match categoryId {}",
                                    subcategoryId, parentId, categoryId);
                        }
                    } catch (NumberFormatException e) {
                        logger.error("getSubcategoryName: Error parsing parent ID: {}", e.getMessage());
                    }
                } else {
                    logger.info("getSubcategoryName: Value does not contain parent ID: {}", subcategory.getValue());
                }
            }
        }

        logger.info("getSubcategoryName: No valid subcategory found for ID {} with parent category {}, returning empty string",
                subcategoryId, categoryId);
        return "";
    }

    private String getSubSubcategoryName(Integer subSubcategoryId, Integer subcategoryId) {
        if (subSubcategoryId == null || subcategoryId == null) {
            logger.info("getSubSubcategoryName: subSubcategoryId or subcategoryId is null, returning empty string");
            return "";
        }

        logger.info("getSubSubcategoryName: Looking for subSubcategoryId {} in component type {}", subSubcategoryId, ConstantsUtil.COMPONENT_TYPE_SEO_SUBSUBCATEGORIES);
        List<MasterData> subSubcategories = masterDataRepository.findByComponentType_Id(ConstantsUtil.COMPONENT_TYPE_SEO_SUBSUBCATEGORIES);

        List<MasterData> matchingSubSubcategories = subSubcategories.stream()
            .filter(data -> data.getMasterDataId() == subSubcategoryId)
            .toList();

        logger.info("getSubSubcategoryName: Found {} entries with masterDataId {}", matchingSubSubcategories.size(), subSubcategoryId);

        for (MasterData subSubcategory : matchingSubSubcategories) {
            if (subSubcategory.getValue() != null) {
                String[] parts = subSubcategory.getValue().split("\\|");
                if (parts.length > 1) {
                    try {
                        int parentId = Integer.parseInt(parts[1]);

                        if (parentId == subcategoryId) {
                            logger.info("getSubSubcategoryName: Found subSubcategory with value: {}, returning part: {}", subSubcategory.getValue(), parts[0]);
                            return parts[0];
                        } else {
                            logger.info("getSubSubcategoryName: Parent mismatch - subSubcategory {} has parent ID {} which does not match subcategoryId {}",
                                    subSubcategoryId, parentId, subcategoryId);
                        }
                    } catch (NumberFormatException e) {
                        logger.error("getSubSubcategoryName: Error parsing parent ID: {}", e.getMessage());
                    }
                } else {
                    logger.info("getSubSubcategoryName: Value does not contain parent ID: {}", subSubcategory.getValue());
                }
            }
        }

        logger.info("getSubSubcategoryName: No valid subSubcategory found for ID {} with parent subcategory {}, returning empty string",
                subSubcategoryId, subcategoryId);
        return "";
    }



    private String generateSeoUrl(String category, String subcategory, String subSubcategory, String customSlug, String title) {
        logger.info("generateSeoUrl - Inputs: category='{}', subcategory='{}', subSubcategory='{}', customSlug='{}', title='{}'",
                category, subcategory, subSubcategory, customSlug, title);

        StringBuilder urlBuilder = new StringBuilder();

        if (customSlug != null && !customSlug.isEmpty()) {
            String friendlySlug = convertToUrlFriendly(customSlug);
            logger.info("Using custom slug: {} -> {}", customSlug, friendlySlug);
            urlBuilder.append(friendlySlug);

            if (title != null && !title.isEmpty()) {
                String friendlyTitle = convertToUrlFriendly(title);
                logger.info("Adding title to custom slug URL: {} -> {}", title, friendlyTitle);
                urlBuilder.append("/").append(friendlyTitle);
            }

            logger.info("URL with custom slug: {}", urlBuilder.toString());
            return urlBuilder.toString();
        }

        if (category != null && !category.isEmpty()) {
            String friendlyCategory = convertToUrlFriendly(category);
            logger.info("Adding category to URL: {} -> {}", category, friendlyCategory);
            urlBuilder.append(friendlyCategory);
        }

        if (subcategory != null && !subcategory.isEmpty()) {
            String friendlySubcategory = convertToUrlFriendly(subcategory);
            if (!urlBuilder.isEmpty()) {
                logger.info("Adding subcategory to URL: {} -> {}", subcategory, friendlySubcategory);
                urlBuilder.append("/");
            }
            urlBuilder.append(friendlySubcategory);
        }

        if (subSubcategory != null && !subSubcategory.isEmpty()) {
            String friendlySubSubcategory = convertToUrlFriendly(subSubcategory);
            if (!urlBuilder.isEmpty()) {
                logger.info("Adding subSubcategory to URL: {} -> {}", subSubcategory, friendlySubSubcategory);
                urlBuilder.append("/");
            }
            urlBuilder.append(friendlySubSubcategory);
        } else {
            logger.info("Skipping subSubcategory in URL as it's empty or null");
        }

        if (title != null && !title.isEmpty()) {
            String friendlyTitle = convertToUrlFriendly(title);
            if (!urlBuilder.isEmpty()) {
                logger.info("Adding title to URL: {} -> {}", title, friendlyTitle);
                urlBuilder.append("/");
            }
            urlBuilder.append(friendlyTitle);
        }

        logger.info("Final generated URL: {}", urlBuilder.toString());
        return urlBuilder.toString();
    }




    private String convertToUrlFriendly(String input) {
        if (input == null) {
            return "";
        }

        String result = input.toLowerCase();

        result = result.replaceAll("\\s+", "-");

        result = result.replaceAll("[^a-z0-9-]", "");

        result = result.replaceAll("-+", "-");

        result = result.replaceAll("^-|-$", "");

        return result;
    }


    private String removeBaseUrl(String url) {
        if (url == null || url.isEmpty()) {
            return "";
        }

        if (url.contains("://")) {
            try {
                java.net.URI uri = new java.net.URI(url);
                String path = uri.getPath();

                if (path.startsWith("/")) {
                    path = path.substring(1);
                }

                logger.info("Removed base URL from '{}', resulting in path: '{}'", url, path);
                return path;
            } catch (Exception e) {
                logger.warn("Failed to parse URL '{}': {}", url, e.getMessage());
            }
        }

        String[] parts = url.split("/", 4);
        if (parts.length >= 3 && (parts[0].equals("http:") || parts[0].equals("https:") || parts[0].isEmpty())) {
            StringBuilder pathBuilder = new StringBuilder();
            for (int i = 3; i < parts.length; i++) {
                if (i > 3) {
                    pathBuilder.append("/");
                }
                pathBuilder.append(parts[i]);
            }
            String path = pathBuilder.toString();
            logger.info("Removed base URL using fallback method from '{}', resulting in path: '{}'", url, path);
            return path;
        }
        return url;
    }


    private Optional<SEOPages> findSeoPageByExactUrl(String url) {
        try {
            String cleanUrl = removeBaseUrl(url);
            logger.info("Looking for SEO page with exact URL: {} (cleaned from: {})", cleanUrl, url);

            Optional<SEOPages> directMatch = seoPageRepo.findBySeoPagesUrl(cleanUrl);
            if (directMatch.isPresent()) {
                logger.info("Found direct match for URL: {}", cleanUrl);
                return directMatch;
            }

            String lowerCaseUrl = cleanUrl.toLowerCase();
            List<SEOPages> allPages = seoPageRepo.findAll();

            for (SEOPages page : allPages) {
                if (page.getSeoPagesUrl() != null) {
                    String pageUrl = page.getSeoPagesUrl().toLowerCase();

                    if (pageUrl.equals(lowerCaseUrl)) {
                        logger.info("Found case-insensitive URL match: {}", page.getSeoPagesUrl());
                        return Optional.of(page);
                    }
                }
            }

            logger.info("No SEO page found with URL: {} (cleaned from: {})", cleanUrl, url);
            return Optional.empty();
        } catch (Exception e) {
            logger.error("Error finding SEO page with URL '{}': {}", url, e.getMessage());
            return Optional.empty();
        }
    }

    public Optional<SEOPages> getSeoPageByExactUrl(
            String category, String subcategory, String subSubcategory, String title) {
        try {
            logger.info("Searching for SEO page. Parameters: category='{}', subcategory='{}', subSubcategory='{}', title='{}'",
                    category, subcategory, subSubcategory, title);

            String categoryParam = isNullOrEmpty(category) ? null : removeBaseUrl(category);
            String subcategoryParam = isNullOrEmpty(subcategory) ? null : removeBaseUrl(subcategory);
            String subSubcategoryParam = isNullOrEmpty(subSubcategory) ? null : removeBaseUrl(subSubcategory);
            String titleParam = isNullOrEmpty(title) ? null : removeBaseUrl(title);

            boolean allParamsEmpty = categoryParam == null && subcategoryParam == null &&
                                    subSubcategoryParam == null && titleParam == null;
            if (allParamsEmpty) {
                logger.info("No search parameters provided, returning empty result");
                return Optional.empty();
            }

            String url = generateSeoUrl(categoryParam, subcategoryParam, subSubcategoryParam, null, titleParam);
            logger.info("Generated URL for search: {}", url);

            Optional<SEOPages> exactMatch = findSeoPageByExactUrl(url);
            if (exactMatch.isPresent()) {
                logger.info("Found exact URL match: {}", exactMatch.get().getSeoPagesUrl());
                return exactMatch;
            }

            logger.info("No exact URL match found, returning empty result");
            return Optional.empty();
        } catch (Exception e) {
            logger.error("Error searching for SEO page: {}", e.getMessage(), e);
            throw e;
        }
    }

    public List<SEOPages> getSeoPagesByCategorySubcategorySubSubcategoryAndTitle(
            String category, String subcategory, String subSubcategory, String title) {
        try {
            logger.info("Searching for SEO pages with parameters: category='{}', subcategory='{}', subSubcategory='{}', title='{}'",
                    category, subcategory, subSubcategory, title);

            String categoryParam = isNullOrEmpty(category) ? null : removeBaseUrl(category);
            String subcategoryParam = isNullOrEmpty(subcategory) ? null : removeBaseUrl(subcategory);
            String subSubcategoryParam = isNullOrEmpty(subSubcategory) ? null : removeBaseUrl(subSubcategory);
            String titleParam = isNullOrEmpty(title) ? null : removeBaseUrl(title);

            boolean allParamsEmpty = categoryParam == null && subcategoryParam == null &&
                                    subSubcategoryParam == null && titleParam == null;

            if (allParamsEmpty) {
                logger.info("No search parameters provided, returning all SEO pages");
                List<SEOPages> allPages = seoPageRepo.findAll();
                logger.info("Found {} total SEO pages", allPages.size());
                return allPages;
            }

            if (categoryParam == null && subcategoryParam == null && subSubcategoryParam == null) {
                logger.info("Only title parameter provided, using simplified search method");
                List<SEOPages> matchingPages = seoPageRepo.findByTitleContaining(titleParam);
                logger.info("Found {} matching SEO pages by title", matchingPages.size());
                return matchingPages;
            }

            if (categoryParam != null) {
                List<MasterData> categories = masterDataRepository.findByComponentType_Id(ConstantsUtil.COMPONENT_TYPE_SEO_CATEGORIES);
                logger.info("Available categories in MasterData: {}",
                    categories.stream().map(m -> m.getMasterDataId() + ":" + m.getValue()).collect(Collectors.joining(", ")));
            }

            if (subcategoryParam != null) {
                List<MasterData> subcategories = masterDataRepository.findByComponentType_Id(ConstantsUtil.COMPONENT_TYPE_SEO_SUBCATEGORIES);
                logger.info("Available subcategories in MasterData: {}",
                    subcategories.stream().map(m -> m.getMasterDataId() + ":" + m.getValue()).collect(Collectors.joining(", ")));
            }

            if (subSubcategoryParam != null) {
                List<MasterData> subSubcategories = masterDataRepository.findByComponentType_Id(ConstantsUtil.COMPONENT_TYPE_SEO_SUBSUBCATEGORIES);
                logger.info("Available subSubcategories in MasterData: {}",
                    subSubcategories.stream().map(m -> m.getMasterDataId() + ":" + m.getValue()).collect(Collectors.joining(", ")));
            }

            logger.info("Using normalized parameters for repository query: category={}, subcategory={}, subSubcategory={}, title={}",
                    categoryParam, subcategoryParam, subSubcategoryParam, titleParam);

            logger.info("Component type IDs: categories={}, subcategories={}, subSubcategories={}",
                    ConstantsUtil.COMPONENT_TYPE_SEO_CATEGORIES,
                    ConstantsUtil.COMPONENT_TYPE_SEO_SUBCATEGORIES,
                    ConstantsUtil.COMPONENT_TYPE_SEO_SUBSUBCATEGORIES);

            List<SEOPages> matchingPages = seoPageRepo.findAllByCategorySubcategoryAndSubSubcategoryAndTitle(
                    categoryParam, subcategoryParam, subSubcategoryParam, titleParam,
                    ConstantsUtil.COMPONENT_TYPE_SEO_CATEGORIES,
                    ConstantsUtil.COMPONENT_TYPE_SEO_SUBCATEGORIES,
                    ConstantsUtil.COMPONENT_TYPE_SEO_SUBSUBCATEGORIES);

            logger.info("Found {} matching SEO pages", matchingPages.size());
            return matchingPages;
        } catch (Exception e) {
            logger.error("Error searching for SEO pages: {}", e.getMessage(), e);
            throw e;
        }
    }

    private boolean isNullOrEmpty(String str) {
        return str == null || str.isEmpty() || str.trim().isEmpty();
    }
}
