package com.job.jobportal.service;

import com.google.api.client.googleapis.auth.oauth2.GoogleIdToken;
import com.google.api.client.googleapis.auth.oauth2.GoogleIdTokenVerifier;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.jackson2.JacksonFactory;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;


import java.io.IOException;
import java.math.BigInteger;
import java.security.GeneralSecurityException;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.RSAPublicKeySpec;
import java.util.*;

import java.net.URL;
import java.security.interfaces.RSAPublicKey;
import java.util.HashMap;
import java.util.Map;

import com.auth0.jwk.Jwk;
import com.auth0.jwk.JwkProvider;
import com.auth0.jwk.UrlJwkProvider;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;

import javax.naming.AuthenticationException;

@Service
public class SocialSignInService {

    private static final String FACEBOOK_TOKEN_VERIFICATION_URL =
            "https://graph.facebook.com/debug_token?input_token={token}&access_token={appAccessToken}";

    private static final String jwksUrl = "https://www.facebook.com/.well-known/oauth/openid/jwks/";


    private static final String FACEBOOK_USER_INFO_URL =
            "https://graph.facebook.com/me?fields=id,name,email&access_token={token}";

    private final String appId = "1456102035268496";
    private final String appSecret = "806fea484971cae1baf6562daeb815cc";

    private static final String APPLE_KEYS_URL = "https://appleid.apple.com/auth/keys";

    @Autowired
    RestTemplate restTemplate;


    public Map<String, Object> validateGoogleAccessToken(String accessToken) throws GeneralSecurityException, IOException, AuthenticationException {
        Map<String, Object> tokenValidationResponse = new HashMap<>();
        GoogleIdTokenVerifier verifier = new GoogleIdTokenVerifier.Builder(new NetHttpTransport(), new JacksonFactory())
                .setAudience(Collections.singletonList("451968071577-6e7fq1bvv60b4l7dmb2mk1v0aghl439b.apps.googleusercontent.com"))
                .build();

        GoogleIdToken idToken = verifier.verify(accessToken);
        if (idToken != null) {
            GoogleIdToken.Payload payload = idToken.getPayload();
            tokenValidationResponse.put("userId", payload.getSubject());
            tokenValidationResponse.put("email", payload.getEmail());
            tokenValidationResponse.put("name", payload.get("name"));
            return tokenValidationResponse;
            // Extract user info from payload
        } else {
            throw new AuthenticationException("Invalid Facebook access token");

        }
    }

    public Map<String, Object> validateFacebookAccessToken(String accessToken) throws AuthenticationException {
        String appAccessToken = appId + "|" + appSecret;

        // Step 1: Validate the token with Facebook
        Map<String, Object> tokenValidationResponse = restTemplate.getForObject(
                FACEBOOK_TOKEN_VERIFICATION_URL, Map.class, accessToken, appAccessToken);

        if (tokenValidationResponse != null && tokenValidationResponse.containsKey("data")) {
            Map<String, Object> data = (Map<String, Object>) tokenValidationResponse.get("data");
            boolean isValid = (boolean) data.get("is_valid");

            if (isValid) {
                // Step 2: Retrieve user information
                return getUserInfoFromFacebook(accessToken);
            } else {
                throw new AuthenticationException("Invalid Facebook access token");
            }
        }

        throw new RuntimeException("Facebook token validation failed");
    }

    public Map<String, Object> validateIosAuthenticationToken(String authToken) throws AuthenticationException {
        try {

            JwkProvider provider = new UrlJwkProvider(new URL(jwksUrl));

            DecodedJWT jwt = JWT.decode(authToken);
            Jwk jwk = provider.get(jwt.getKeyId());

            Algorithm algorithm = Algorithm.RSA256((RSAPublicKey) jwk.getPublicKey(), null);

            // Extract actual issuer from token
            String actualIssuer = jwt.getIssuer();
            System.out.println("Issuer from token: " + actualIssuer);

            JWTVerifier verifier = JWT.require(algorithm)
                    .withIssuer(actualIssuer) // ✅ Use extracted issuer
                    .build();

            verifier.verify(authToken);

            // Extract user data from token
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("sub", jwt.getSubject());
            userInfo.put("email", jwt.getClaim("email").asString());
            userInfo.put("name", jwt.getClaim("name").asString());

            return userInfo;
        } catch (Exception e) {
            throw new AuthenticationException("Invalid iOS Facebook Authentication Token: " + e.getMessage());
        }
    }

    public Map<String, Object> validateAppleAccessToken(String accessToken) {
        Map<String, Object> tokenValidationResponse = verifyIdentityToken(accessToken);
        return tokenValidationResponse;

    }

    private Map<String, Object> getUserInfoFromFacebook(String accessToken) {
        // Retrieve user information from Facebook
        return restTemplate.getForObject(FACEBOOK_USER_INFO_URL, Map.class, accessToken);
    }


    public Map<String, Object> verifyIdentityToken(String identityToken) {
        try {
            // Fetch Apple's public keys
            Map<String, Object> publicKeys = restTemplate.getForObject(APPLE_KEYS_URL, Map.class);

            // Extract the key ID from the token header
            String keyId = Jwts.parser()
                    .build()
                    .parseClaimsJws(identityToken)
                    .getHeader()
                    .getKeyId();

            // Cast the "keys" object to a List of Maps
            List<Map<String, String>> keys = (List<Map<String, String>>) publicKeys.get("keys");

            // Match the key ID with Apple's keys
            Map<String, String> matchingKey = keys.stream()
                    .filter(key -> keyId.equals(key.get("kid")))
                    .findFirst()
                    .orElseThrow(() -> new IllegalArgumentException("Invalid key ID"));

            String n = matchingKey.get("n");
            String e = matchingKey.get("e");

            PublicKey publicKey = constructPublicKey(n, e);

            // Parse the identity token and validate it using Apple's public key
            Claims claims = Jwts.parser()
                    .setSigningKey(publicKey)
                    .build()
                    .parseClaimsJws(identityToken)
                    .getBody();

            return new HashMap<>(claims);
        } catch (Exception e) {
            throw new RuntimeException("Token verification failed", e);
        }
    }

    private PublicKey constructPublicKey(String n, String e) throws Exception {
        byte[] modulusBytes = Base64.getUrlDecoder().decode(n);
        byte[] exponentBytes = Base64.getUrlDecoder().decode(e);

        BigInteger modulus = new BigInteger(1, modulusBytes);
        BigInteger publicExponent = new BigInteger(1, exponentBytes);

        RSAPublicKeySpec keySpec = new RSAPublicKeySpec(modulus, publicExponent);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePublic(keySpec);
    }
}
