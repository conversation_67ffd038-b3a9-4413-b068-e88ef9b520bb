package com.job.jobportal.controller;

import com.job.jobportal.dto.SMSTemplateDTO;
import com.job.jobportal.dto.SendMarketingDTO;
import com.job.jobportal.model.SMSTemplate;
import com.job.jobportal.response.ApiResponse;
import com.job.jobportal.response.BadRequestException;
import com.job.jobportal.service.SmsTemplateService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
public class SmsTemplateController {

    @Autowired
    private SmsTemplateService smsTemplateService;

    private static final Logger logger = LoggerFactory.getLogger(SmsTemplateController.class);

    @Autowired
    MessageSource message;


    @PostMapping("/sendsms")
    public ResponseEntity<?> sendSMS(@RequestBody SendMarketingDTO sendMarketingDTO) {
        try {
            smsTemplateService.sendMessage(sendMarketingDTO);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null, message.getMessage("msg.SMS_sent_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);

        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/sendwhatsapp")
    public ResponseEntity<?> sendWhatsapp(@RequestBody SendMarketingDTO sendMarketingDTO) {
        try {
            smsTemplateService.sendWhatsapp(sendMarketingDTO);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null, message.getMessage("msg.SMS_sent_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);

        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/smstemplate")
    public ResponseEntity<?> getAllSMStemplate() {
        List<SMSTemplate> marketingUserDTOS = smsTemplateService.getAllSMSTemplate();
        return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, marketingUserDTOS, message.getMessage("msg.SMStemplate_get_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
    }

    @PostMapping("/smstemplate")
    public ResponseEntity<?> addSmsTemplate(@RequestBody SMSTemplateDTO smsTemplateDTO) {
        try {
            SMSTemplate emailTemplate = smsTemplateService.addSMSTemplate(smsTemplateDTO);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, emailTemplate, message.getMessage("msg.SMStemplate_added_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);

        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PutMapping("/smstemplate")
    public ResponseEntity<?> updateSmsTemplate(@RequestBody SMSTemplateDTO smsTemplateDTO) {
        try {
            SMSTemplate emailTemplate = smsTemplateService.updateSMSTemplate(smsTemplateDTO);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, emailTemplate, message.getMessage("msg.SMStemplate_updated_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);

        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @DeleteMapping("/smstemplate/{templateId}")
    public ResponseEntity<?> deleteSMSTemplate(@PathVariable("templateId") Long templateId) {
        try {
            smsTemplateService.deleteSmsTemplate(templateId);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null, message.getMessage("msg.SMStemplate_deleted_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);

        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

}
