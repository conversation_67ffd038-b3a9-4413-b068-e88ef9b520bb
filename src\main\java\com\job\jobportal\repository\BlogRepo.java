package com.job.jobportal.repository;


import com.job.jobportal.model.BlogTopic;
import com.job.jobportal.model.BlogWebsite;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;


@Repository
public interface BlogRepo extends JpaRepository<BlogWebsite, Long> {

    @Query("Select c  from BlogWebsite c WHERE   (:blogTitle is null or c.blogTitle like %:blogTitle% ) Order by c.blogCreatedDate desc")
    Page<BlogTopic> findAllBlogTopic(@Param("blogTitle") String blogTitle, Pageable pageable);

    @Query("SELECT b.id FROM BlogWebsite b WHERE b.id < :blogId ORDER BY b.id DESC")
    Page<Long> findPreviousBlogId(@Param("blogId") Long blogId, Pageable pageable);

    @Query("SELECT b.id FROM BlogWebsite b WHERE b.id > :blogId ORDER BY b.id ASC")
    Page<Long> findNextBlogId(@Param("blogId") Long blogId, Pageable pageable);



}
