package com.job.jobportal.dto;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class BlogDTO {

    private Long blogId;

    private String blogTitle;

    private String blogUrl;

    private String metaDescription;

    private String metaKeyWord;

    private String featuredImage;

    private String featuredImageKey;

    private String thumbnailImageKey;

    private String thumbnailImage;


    private JsonNode blogDescription;

    private String blogTags;

    private Timestamp blogCreatedDate;


}
