package com.job.jobportal.repository;

import com.job.jobportal.model.MasterData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface MasterDataRepository extends JpaRepository<MasterData, Integer> {
    List<MasterData> findByComponentType_IdIn(List<Integer> componentTypeIds);

    MasterData findByComponentType_IdAndMasterDataId(Integer componentTypeId, Integer masterDataId);

    List<MasterData> findByComponentType_Id(int i);

    MasterData findByComponentType_IdAndValue(Integer componentTypeId, String value);

    @Query("SELECT m FROM MasterData m WHERE m.componentType.id = :componentTypeId AND m.masterDataId = :masterDataId")
    List<MasterData> findByComponentType_IdAndMasterDataIdlist(int componentTypeId, int masterDataId);

}



