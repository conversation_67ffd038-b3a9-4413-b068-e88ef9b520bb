package com.job.jobportal.controller;

import com.job.jobportal.dto.*;
import com.job.jobportal.model.MasterData;
import com.job.jobportal.model.ProgrammaticPage;
import com.job.jobportal.repository.MasterDataRepository;
import com.job.jobportal.response.ApiResponse;
import com.job.jobportal.response.BadRequestException;
import com.job.jobportal.service.MasterDataService;
import com.job.jobportal.service.ProgrammaticPageService;
import com.job.jobportal.service.SeoGenerateService;

import com.job.jobportal.util.ConstantsUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/programmatic-pages")
public class ProgrammaticPageController {

    @Autowired
    private ProgrammaticPageService programmaticPageService;

    @Autowired
    private MasterDataService masterDataService;

    @Autowired
    private MasterDataRepository masterDataRepository;

    @Autowired
    private MessageSource messageSource;

    @Autowired
    private SeoGenerateService seoGenerateService;

    private static final Logger logger = LoggerFactory.getLogger(ProgrammaticPageController.class);

    @PostMapping
    public ResponseEntity<?> addProgrammaticPage(@RequestBody ProgrammaticPageDTO dto) {
        try {
            ProgrammaticPage page = programmaticPageService.addProgrammaticPage(dto);
            return new ResponseEntity<>(
                    new ApiResponse<>(
                            HttpStatus.CREATED,
                            true,
                            page,
                            messageSource.getMessage("msg.programmatic_page_created", null, LocaleContextHolder.getLocale())
                    ),
                    HttpStatus.CREATED
            );
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()),
                    HttpStatus.BAD_REQUEST
            );
        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @PutMapping
    public ResponseEntity<?> updateProgrammaticPage(@RequestBody ProgrammaticPageDTO dto) {
        try {
            ProgrammaticPage page = programmaticPageService.updateProgrammaticPage(dto);
            return new ResponseEntity<>(
                    new ApiResponse<>(
                            HttpStatus.OK,
                            true,
                            page,
                            messageSource.getMessage("msg.programmatic_page_updated", null, LocaleContextHolder.getLocale())
                    ),
                    HttpStatus.OK
            );
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()),
                    HttpStatus.BAD_REQUEST
            );
        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteProgrammaticPage(@PathVariable Long id) {
        try {
            programmaticPageService.deleteProgrammaticPage(id);
            return new ResponseEntity<>(
                    new ApiResponse<>(
                            HttpStatus.OK,
                            true,
                            null,
                            messageSource.getMessage("msg.programmatic_page_deleted", null, LocaleContextHolder.getLocale())
                    ),
                    HttpStatus.OK
            );
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()),
                    HttpStatus.BAD_REQUEST
            );
        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @GetMapping
    public ResponseEntity<?> getAllProgrammaticPages(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String title) {
        try {
            Map<String, Object> result = programmaticPageService.getAllProgrammaticPages(page, size, title);
            return new ResponseEntity<>(
                    new ApiResponse<>(
                            HttpStatus.OK,
                            true,
                            result,
                            messageSource.getMessage("msg.programmatic_pages_fetched", null, LocaleContextHolder.getLocale())
                    ),
                    HttpStatus.OK
            );
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()),
                    HttpStatus.BAD_REQUEST
            );
        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getProgrammaticPageById(@PathVariable Long id) {
        try {
            ProgrammaticPage page = programmaticPageService.getProgrammaticPageById(id);
            return new ResponseEntity<>(
                    new ApiResponse<>(
                            HttpStatus.OK,
                            true,
                            page,
                            messageSource.getMessage("msg.programmatic_page_fetched", null, LocaleContextHolder.getLocale())
                    ),
                    HttpStatus.OK
            );
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()),
                    HttpStatus.BAD_REQUEST
            );
        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @GetMapping("/url/{url}")
    public ResponseEntity<?> getProgrammaticPageByUrl(@PathVariable String url) {
        try {
            ProgrammaticPage page = programmaticPageService.getProgrammaticPageByUrl(url);
            return new ResponseEntity<>(
                    new ApiResponse<>(
                            HttpStatus.OK,
                            true,
                            page,
                            messageSource.getMessage("msg.programmatic_page_fetched", null, LocaleContextHolder.getLocale())
                    ),
                    HttpStatus.OK
            );
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()),
                    HttpStatus.BAD_REQUEST
            );
        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @GetMapping("/search")
    public ResponseEntity<?> getProgrammaticPageByParams(
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String subcategory,
            @RequestParam(required = false) String subSubcategory,
            @RequestParam(required = false) String city) {
        try {
            logger.info("Controller received search request with raw parameters: category='{}', subcategory='{}', subSubcategory='{}', city='{}'",
                    category, subcategory, subSubcategory, city);

            logger.info("Parameter details: category=[type={}, length={}], subcategory=[type={}, length={}], subSubcategory=[type={}, length={}], city=[type={}, length={}]",
                    category != null ? category.getClass().getName() : "null", category != null ? category.length() : -1,
                    subcategory != null ? subcategory.getClass().getName() : "null", subcategory != null ? subcategory.length() : -1,
                    subSubcategory != null ? subSubcategory.getClass().getName() : "null", subSubcategory != null ? subSubcategory.length() : -1,
                    city != null ? city.getClass().getName() : "null", city != null ? city.length() : -1);

            List<ProgrammaticPage> pages = programmaticPageService.getProgrammaticPageByCategorySubcategoryCountry(
                    category, subcategory, subSubcategory, city);

            String message;
            if (pages.isEmpty()) {
                message = messageSource.getMessage("msg.no_programmatic_pages_found", null, "No programmatic pages found", LocaleContextHolder.getLocale());
            } else if (pages.size() == 1) {
                message = messageSource.getMessage("msg.programmatic_page_fetched", null, "Programmatic page fetched successfully", LocaleContextHolder.getLocale());
            } else {
                message = messageSource.getMessage("msg.programmatic_pages_fetched", null, "Programmatic pages fetched successfully", LocaleContextHolder.getLocale());
            }

            return new ResponseEntity<>(
                    new ApiResponse<>(
                            HttpStatus.OK,
                            true,
                            pages,
                            message
                    ),
                    HttpStatus.OK
            );
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()),
                    HttpStatus.BAD_REQUEST
            );
        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @GetMapping("/categories")
    public ResponseEntity<?> getProgrammaticCategories() {
        try {
            List<MasterDataDto> categories = programmaticPageService.getProgrammaticCategories();

            String message;
            try {
                message = messageSource.getMessage("msg.programmatic_categories_fetched", null, LocaleContextHolder.getLocale());
            } catch (Exception ex) {
                message = "Programmatic categories fetched successfully";
            }

            return new ResponseEntity<>(
                    new ApiResponse<>(
                            HttpStatus.OK,
                            true,
                            categories,
                            message
                    ),
                    HttpStatus.OK
            );
        } catch (Exception e) {
            logger.error("Error fetching programmatic categories: {}", e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, "Error fetching programmatic categories: " + e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @GetMapping("/city-state-combinations")
    public ResponseEntity<?> getCityStateCombinations() {
        try {
            List<String> locations = programmaticPageService.getCityStateCombinationsAsArrays();

            String message;
            try {
                message = messageSource.getMessage("msg.city_state_combinations_fetched", null, LocaleContextHolder.getLocale());
            } catch (Exception ex) {
                message = "Locations fetched successfully";
            }

            return new ResponseEntity<>(
                    new ApiResponse<>(
                            HttpStatus.OK,
                            true,
                            locations,
                            message
                    ),
                    HttpStatus.OK
            );
        } catch (Exception e) {
            logger.error("Error fetching locations: {}", e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, "Error fetching locations: " + e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @GetMapping("/subcategories/{categoryId}")
    public ResponseEntity<?> getProgrammaticSubcategories(@PathVariable Integer categoryId) {
        try {
            List<MasterDataDto> subcategories = masterDataService.getSubcategoriesForCategory(categoryId);

            if (subcategories == null) {
                subcategories = new ArrayList<>();
            }

            String message;
            try {
                message = messageSource.getMessage("msg.programmatic_subcategories_fetched", null, LocaleContextHolder.getLocale());
            } catch (Exception ex) {
                message = "Programmatic subcategories fetched successfully";
            }

            return new ResponseEntity<>(
                    new ApiResponse<>(
                            HttpStatus.OK,
                            true,
                            subcategories,
                            message
                    ),
                    HttpStatus.OK
            );
        } catch (Exception e) {
            logger.error("Error fetching subcategories for category ID {}: {}", categoryId, e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, "Error fetching subcategories: " + e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @GetMapping("/subsubcategories/{subcategoryId}")
    public ResponseEntity<?> getProgrammaticSubSubcategories(@PathVariable Integer subcategoryId) {
        try {
            List<MasterDataDto> subSubcategories = masterDataService.getJobSubSubcategories(subcategoryId);

            if (subSubcategories == null) {
                subSubcategories = new ArrayList<>();
            }

            String message;
            try {
                message = messageSource.getMessage("msg.programmatic_subsubcategories_fetched", null, LocaleContextHolder.getLocale());
            } catch (Exception ex) {
                message = "Programmatic sub-subcategories fetched successfully";
            }

            return new ResponseEntity<>(
                    new ApiResponse<>(
                            HttpStatus.OK,
                            true,
                            subSubcategories,
                            message
                    ),
                    HttpStatus.OK
            );
        } catch (Exception e) {
            logger.error("Error fetching sub-subcategories for subcategory ID {}: {}", subcategoryId, e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, "Error fetching sub-subcategories: " + e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @PostMapping("/seo/generate")
    public ResponseEntity<?> generateSeo(@RequestBody SeoGenerateRequest request, Locale locale) {
        try {
            logger.info("Received request to generate SEO metadata for job title: {}, category: {}, subcategory: {}, city: {}",
                    request.getJobTitle(), request.getCategory(), request.getSubcategory(), request.getCity());

            SeoGenerateResponse response = seoGenerateService.generateSeoMetadata(request);

            logger.info("Successfully generated SEO metadata. URL: {}", response.getSeoUrl());

            String message = messageSource.getMessage(
                "msg.seo_metadata_generated",
                null,
                "SEO metadata generated successfully",
                locale
            );

            return ResponseEntity.ok(new ApiResponse<>(
                HttpStatus.OK,
                true,
                response,
                message
            ));
        } catch (IllegalArgumentException e) {
            logger.error("Validation error generating SEO metadata: {}", e.getMessage());

            return ResponseEntity.badRequest()
                .body(new ApiResponse<>(
                    HttpStatus.BAD_REQUEST,
                    false,
                    null,
                    e.getMessage()
                ));
        } catch (Exception e) {
            logger.error("Error generating SEO metadata: {}", e.getMessage(), e);

            String errorMessage = messageSource.getMessage(
                "msg.error_generating_seo_metadata",
                null,
                "Error generating SEO metadata",
                locale
            );

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ApiResponse<>(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    false,
                    null,
                    errorMessage + ": " + e.getMessage()
                ));
        }
    }

    @PostMapping("/seo/bulk-generate")
    public ResponseEntity<?> generateBulkSeo(@RequestBody BulkSeoGenerateRequest request, Locale locale) {
        try {
            logger.info("Received request to generate bulk SEO metadata for {} items", request.getRequests().size());
            BulkSeoGenerateResponse response = seoGenerateService.generateBulkSeoMetadata(request);
            String message = messageSource.getMessage("msg.bulk_seo_metadata_generated", null, "Bulk SEO metadata generated successfully", locale);
            return ResponseEntity.ok(new ApiResponse<>(HttpStatus.OK, true, response, message));
        } catch (Exception e) {
            logger.error("Error generating bulk SEO metadata: {}", e.getMessage(), e);
            String errorMessage = messageSource.getMessage("msg.error_generating_bulk_seo_metadata", null, "Error generating bulk SEO metadata", locale);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, errorMessage + ": " + e.getMessage()));
        }
    }
}
