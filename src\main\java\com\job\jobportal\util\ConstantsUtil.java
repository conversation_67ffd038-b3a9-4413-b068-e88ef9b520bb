package com.job.jobportal.util;

import java.util.Map;

public class ConstantsUtil {


    //types of user
    public static final int USER_ACTIVE = 1;

    public static final int USER_INACTIVE = 0;

    //baby gender type
    public static final int PROFILE_GENDER_BOY = 1;

    public static final int PROFILE_GENDER_GIRL = 2;


    //premium account
    public static final int PREMIUM_ACCOUNT_ACTIVE = 1;

    public static final int PREMIUM_ACCOUNT_ACTIVE_INACTIVE = 0;


    //account delete
    public static final int DELETE_ACCOUNT_ACTIVE = 1;

    public static final int DELETE_ACCOUNT_ACTIVE_INACTIVE = 0;

    //storage function
    public static final int ADD_FILE_STORAGE = 1;

    public static final int REMOVE_FILE_STORAGE = -1;


    //types of roles
    public static final String USER_ROLE = "USER";
    public static final String ADMIN_ROLE = "ADMIN";

    public static final String STAFF_ROLE = "STAFF";

    //Addon  Type
    public static final int ADDON_VACCINE = 0;
    public static final int ADDON_STORY = 1;
    public static final int ADDON_TOY = 2;


    //Feedback Start Type
    public static final int FEEDBACK_EMPTY = 0;
    public static final int FEEDBACK_POOR = 1;
    public static final int FEEDBACK_AVERAGE = 2;
    public static final int FEEDBACK_GOOD = 3;
    public static final int FEEDBACK_EXCELLENT = 4;

    //Likes  and comments Type
    public static final int POST_LIKE = 1;
    public static final int POST_UNLIKE = 0;

    //Issue  Type
    public static final int ISSUE_P1 = 1;
    public static final int ISSUE_P2 = 2;
    public static final int ISSUE_P3 = 3;

        //Login  Type
    public static final int LOGIN_GOOGLE = 1;
    public static final int LOGIN_FACEBOOK = 2;
    public static final int LOGIN_APPLE = 3;

    //Issue  Category
    public static final int ISSUE_ORDER_RELATED = 1;
    public static final int ISSUE_DELIVERY_RELATED = 2;
    public static final int ISSUE_PAYMENT_RELATED = 3;
    public static final int ISSUE_STORAGE_RELATED = 4;
    public static final int ISSUE_OTHERS_RELATED = 5;

    //Upload File Type
    public static final int UPLOAD_FILE_IMAGE = 1;
    public static final int UPLOAD_FILE_AUDIO = 2;
    public static final int UPLOAD_FILE_VIDEO = 3;


    //letter Draft /send
    public static final int LETTER_DRAFT = 0;
    public static final int LETTER_SEND = 1;

    //diary note private /public
    public static final int DIARY_PRIVATE = 0;
    public static final int DIARY_PUBLIC = 1;

    //file upload module
    public static final int DIARY_UPLOAD = 1;
    public static final int LETTER_UPLOAD = 2;
    public static final int MILESTONE_UPLOAD = 3;
    public static final int TAGS_UPLOAD = 4;
    public static final int PROFILE_UPLOAD = 5;

    //file upload file type
    public static final int FILE_IMAGE_UPLOAD = 1;
    public static final int FILE_AUDIO_UPLOAD = 2;
    public static final int FILE_VIDEO_UPLOAD = 3;

    //subscription plan type
    public static final int SUBSCRIPTION_FREE_PLAN = 0;     // No permissions (000000)
    public static final int SUBSCRIPTION_BASIC_PLAN = 1;    // Not used, no permissions (000000)
    public static final int SUBSCRIPTION_STANDARD_PLAN = 2; // VIEW_APPLICANTS, POST_JOBS, FEATURED_JOBS, BULK_ACTIONS (101110)
    public static final int SUBSCRIPTION_PREMIUM_PLAN = 3;  // VIEW_APPLICANTS, CONTACT_APPLICANTS, POST_JOBS, FEATURED_JOBS, ANALYTICS (111110)
    public static final int SUBSCRIPTION_ENTERPRISE_PLAN = 4; // All permissions (111111)
    public static final int SUBSCRIPTION_TRIAL_PLAN = 5;    // Same as Standard Plan (101110)

    //subscription status type
    public static final int SUBSCRIPTION_STATUS_NOT_CREATED = 0;
    public static final int SUBSCRIPTION_STATUS_ACTIVE = 1;
    public static final int SUBSCRIPTION_STATUS_CANCELLED = 2;
    public static final int SUBSCRIPTION_STATUS_SUSPENDED = 3;
    public static final int SUBSCRIPTION_STATUS_CREATED_NOT_PAYED = 4;
    public static final int SUBSCRIPTION_STATUS_TRIAL = 5;
    public static final int SUBSCRIPTION_STATUS_PENDING = 6;
    public static final int SUBSCRIPTION_STATUS_PAYMENT_FAILED = 7;

    //subscription limit
    public static final int SUBSCRIPTION_LIMIT_FREE_PLAN = 1024;
    public static final int SUBSCRIPTION_LIMIT_BASIC_PLAN = 2;
    public static final int SUBSCRIPTION_LIMIT_STANDARD_PLAN = 15;
    public static final int SUBSCRIPTION_LIMIT_PREMIUM_PLAN = 50;
    public static final int SUBSCRIPTION_UNLIMITED_PLAN = 4;
    public static final int SUBSCRIPTION_LIMIT_UNLIMITED_PLAN = 200;
    public static final int SUBSCRIPTION_LIMIT_TRIAL_PLAN = 15; // Same as Standard Plan

    // Job post limits by subscription plan
    public static final int JOB_POSTS_LIMIT_FREE_PLAN = 0;
    public static final int JOB_POSTS_LIMIT_BASIC_PLAN = 0;
    public static final int JOB_POSTS_LIMIT_STANDARD_PLAN = 5;
    public static final int JOB_POSTS_LIMIT_PREMIUM_PLAN = 10;
    public static final int JOB_POSTS_LIMIT_ENTERPRISE_PLAN = Integer.MAX_VALUE; // Unlimited
    public static final int JOB_POSTS_LIMIT_TRIAL_PLAN = 5; // Same as Standard Plan

    //Notification  Type
    public static final int NOTIFICATION_LIKE = 1;
    public static final int NOTIFICATION_COMMENT = 2;
    public static final int NOTIFICATION_UPLOAD = 3;
    public static final int NOTIFICATION_LINKED= 4;

    //permission and components
    public static int COMPONENT_TYPE_BLOG=0;
    public static int COMPONENT_TYPE_SEO=1;
    public static int COMPONENT_TYPE_CANDIDATE=2;
    public static int COMPONENT_TYPE_RECRUITER=3;
    public static int COMPONENT_TYPE_JOBS=4;

    public static int PERMISSION_TYPE_READ=0;
    public static int PERMISSION_TYPE_WRITE=1;
    public static int PERMISSION_TYPE_APPROVE=2;
    public static int PERMISSION_TYPE_DELETE=3;

    // Subscription permission positions
    public static final int PERMISSION_VIEW_APPLICANTS = 0;
    public static final int PERMISSION_CONTACT_APPLICANTS = 1;
    public static final int PERMISSION_POST_JOBS = 2;
    public static final int PERMISSION_FEATURED_JOBS = 3;
    public static final int PERMISSION_ANALYTICS = 4;
    public static final int PERMISSION_BULK_ACTIONS = 5;

    // Default permission strings
    public static final String DEFAULT_FREE_PLAN_PERMISSIONS = "000000"; // No permissions

    // Component Types
    public static final int COMPONENT_TYPE_JOB_TYPE = 13;
    public static final int COMPONENT_TYPE_SALARY_CURRENCY = 14;
    public static final int COMPONENT_TYPE_PAY_TYPE = 15;

    public static final int COMPONENT_TYPE_SEO_CATEGORIES = 21;
    public static final int COMPONENT_TYPE_SEO_SUBCATEGORIES = 22;
    public static final int COMPONENT_TYPE_SEO_SUBSUBCATEGORIES = 25;
    public static final int JOB_CATEGORIES_COMPONENT_TYPE_ID = 12;
    public static final int JOB_SUBCATEGORIES_COMPONENT_TYPE_ID = 17;
    public static final int JOB_SUBSUBCATEGORIES_COMPONENT_TYPE_ID = 23;
    public static final int LOCATIONS_COMPONENT_TYPE_ID = 3;
    public static final int DISTRICTS_COMPONENT_TYPE_ID = 24;
    public static final int SKILLS_COMPONENT_TYPE_ID = 1; // Used for job titles

    public static final int COMPONENT_TYPE_PERMISSION_TYPES = 16;
    public static final int COMPONENT_TYPE_SUBSCRIPTION_PLAN_PERMISSIONS = 17;


    // Job Types
    public static final String JOB_TYPE_FULL_TIME = "Full-time";
    public static final String JOB_TYPE_PART_TIME = "Part-time";
    public static final String JOB_TYPE_PERMANENT = "Permanent";
    public static final String JOB_TYPE_CONTRACT = "Contract";
    public static final String JOB_TYPE_TEMPORARY = "Temporary";
    public static final String JOB_TYPE_TRAINING = "Training";
    public static final String JOB_TYPE_FREELANCER = "Freelancer";
    public static final String JOB_TYPE_VOLUNTEER = "Volunteer";

    // Job Type Map for validation
    public static final Map<String, String> JOB_TYPE_MAP = Map.of(
        JOB_TYPE_FULL_TIME, "Full-time",
        JOB_TYPE_PART_TIME, "Part-time",
        JOB_TYPE_PERMANENT, "Permanent",
        JOB_TYPE_CONTRACT, "Contract",
        JOB_TYPE_TEMPORARY, "Temporary",
        JOB_TYPE_TRAINING, "Training",
        JOB_TYPE_FREELANCER, "Freelancer",
        JOB_TYPE_VOLUNTEER, "Volunteer"
    );

    // Career Levels
    public static final String CAREER_LEVEL_ENTRY = "Entry-Level";
    public static final String CAREER_LEVEL_MID = "Mid-Level";
    public static final String CAREER_LEVEL_SENIOR = "Senior";

    // Genders
    public static final String GENDER_MALE = "Male";
    public static final String GENDER_FEMALE = "Female";
    public static final String GENDER_ANY = "Any";

    // Industries
    public static final String INDUSTRY_TECHNOLOGY = "Technology";
    public static final String INDUSTRY_FINANCE = "Finance";
    public static final String INDUSTRY_MARKETING = "Marketing";

    // Qualifications
    public static final String QUALIFICATION_BACHELORS = "Bachelor's Degree";
    public static final String QUALIFICATION_MASTERS = "Master's Degree";
    public static final String QUALIFICATION_PHD = "PhD";

    // For validation or mapping in the backend
    public static final Map<String, String> CAREER_LEVEL_MAP = Map.of(
            CAREER_LEVEL_ENTRY, "Entry-Level",
            CAREER_LEVEL_MID, "Mid-Level",
            CAREER_LEVEL_SENIOR, "Senior"
    );

    public static final Map<String, String> GENDER_MAP = Map.of(
            GENDER_MALE, "Male",
            GENDER_FEMALE, "Female",
            GENDER_ANY, "Any"
    );

    public static final Map<String, String> INDUSTRY_MAP = Map.of(
            INDUSTRY_TECHNOLOGY, "Technology",
            INDUSTRY_FINANCE, "Finance",
            INDUSTRY_MARKETING, "Marketing"
    );

    public static final Map<String, String> QUALIFICATION_MAP = Map.of(
            QUALIFICATION_BACHELORS, "Bachelor's Degree",
            QUALIFICATION_MASTERS, "Master's Degree",
            QUALIFICATION_PHD, "PhD"
    );

    // Salary Currencies
    public static final String CURRENCY_GBP = "GBP";
    public static final String CURRENCY_USD = "USD";
    public static final String CURRENCY_EUR = "EUR";
    public static final String CURRENCY_AUD = "AUD";
    public static final String CURRENCY_CAD = "CAD";
    public static final String CURRENCY_INR = "INR";

    // Pay Types
    public static final String PAY_TYPE_HOURLY = "Hourly";
    public static final String PAY_TYPE_WEEKLY = "Weekly";
    public static final String PAY_TYPE_MONTHLY = "Monthly";

    // Currency Map for validation
    public static final Map<String, String> CURRENCY_MAP = Map.of(
        CURRENCY_GBP, "GBP",
        CURRENCY_USD, "USD",
        CURRENCY_EUR, "EUR",
        CURRENCY_AUD, "AUD",
        CURRENCY_CAD, "CAD",
        CURRENCY_INR, "INR"
    );

    // Pay Type Map for validation
    public static final Map<String, String> PAY_TYPE_MAP = Map.of(
        PAY_TYPE_HOURLY, "Hourly",
        PAY_TYPE_WEEKLY, "Weekly",
        PAY_TYPE_MONTHLY, "Monthly"
    );
}
