package com.job.jobportal.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubscriptionResponseDTO {
    //private String token;
    private String email;
    private int subscriptionStatus;
    private int subscriptionPlanType;
    private String permissions;
    private Date currentPeriodStart;
    private Date currentPeriodEnd;
    private boolean isTrial;
    private Long daysRemaining;

    private int jobPostsLimit;
    private int jobPostsRemaining;
    private Date jobPostsResetDate;
}
