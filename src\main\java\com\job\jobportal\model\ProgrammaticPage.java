package com.job.jobportal.model;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.sql.Timestamp;

@Entity
@Data
public class ProgrammaticPage {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long programmaticPageId;

    private String programmaticPageTitle;

    private String programmaticPageUrl;

    private String programmaticPageMetaTitle;

    private String programmaticPageMetaDescription;

    private Integer programmaticPageCategoryId;

    private Integer programmaticPageSubcategoryId;

    private Integer programmaticPageSubSubcategoryId;

    private Integer programmaticPageCityId;
    private String programmaticPageCity;

    private Integer programmaticPageDistrictId;
    private String programmaticPageDistrict;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "json")
    private JsonNode programmaticPageContent;

    private Timestamp programmaticPageCreatedDate;

    private Timestamp programmaticPageUpdatedDate;

    private Boolean programmaticPageIsActive;
}
