package com.job.jobportal.dto;

import lombok.*;
import lombok.Setter;
import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class JobApplicationDTO {
    private Long applicationId;
    private Long candidateProfileId;
    private Long companyProfileId;
    private Long jobPostId;
    private String jobTitle;
    private String industryValue;
    private String jobLocation;
    private String companyName;
    private String companyLogoUrl;
    private String skills;
    private String resumeUrl;
    private String resumeKey;
    private Integer statusId;
    private String statusValue;
    private Timestamp appliedDate;
    private Timestamp updatedDate;
    private Integer jobCategoryId;
    private String jobCategoryName;
    private Integer jobSubCategoryId;
    private String jobSubCategoryName;
    private Integer jobSubSubCategoryId;
    private String jobSubSubCategoryName;
    private String completeAddress;
    private Integer salaryCurrencyId;
    private String salaryCurrencyName;
    private Integer experienceId;
    private String experienceName;

    private Long companyActiveJobCount;
    private Integer jobTypeId;
    private String jobTypeName;
    private Integer careerLevelId;
    private String careerLevelName;
    private Integer industryId;
    private String industryName;
    private Integer qualificationId;
    private String qualificationName;
    private Integer payTypeId;
    private String payTypeName;
    private Integer departmentId;
    private String departmentName;
    private String timeFilterName;
    private boolean hasCandidateProfile;
}