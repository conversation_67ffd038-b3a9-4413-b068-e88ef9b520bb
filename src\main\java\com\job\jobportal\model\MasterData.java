package com.job.jobportal.model;

import jakarta.persistence.*;
import lombok.Data;

@Entity
@Table(name = "master_data")
@Data
public class MasterData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    @Column(name = "master_data_id")
    private int masterDataId;

    private String value;

    @ManyToOne
    @JoinColumn(name = "component_type_id", nullable = false)
    private ComponentType componentType;
}