package com.job.jobportal.repository;

import com.job.jobportal.model.OTP;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface OtpRepo extends JpaRepository<OTP,Long> {

    Optional<OTP> findByEmailId(String emailId);

    List<OTP> findAllByEmailId(String emailId);

    @Query("SELECT o FROM OTP o WHERE o.emailId = :emailId ORDER BY o.expirationTime DESC")
    List<OTP> findMostRecentByEmailId(String emailId);

}
