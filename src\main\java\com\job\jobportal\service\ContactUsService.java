package com.job.jobportal.service;

import com.job.jobportal.dto.ContactUsDTO;
import com.job.jobportal.model.ContactUsSubmission;
import com.job.jobportal.repository.ContactUsSubmissionRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ContactUsService {

    private static final Logger logger = LoggerFactory.getLogger(ContactUsService.class);

    @Autowired
    private EmailService emailService;

    @Autowired
    private ContactUsSubmissionRepository contactUsSubmissionRepository;

    @Value("${application.email}")
    private String defaultEmail;

    @Value("${email.domain.from")
    private String domainEmail;

    @Value("${email.provider}")
    private String emailProvider;

    @Value("${email.domain.from}")
    private String supportEmail;

    private String getFromEmail() {
        return "gmail".equalsIgnoreCase(emailProvider) ? defaultEmail : domainEmail;
    }

    @Transactional
    public void processContactUsForm(ContactUsDTO contactUsDTO) {
        try {
            String from = getFromEmail();
            String to = supportEmail;
            String subject = contactUsDTO.getSubject();
            String body = "Name: " + contactUsDTO.getName() + "\n" +
                    "Email: " + contactUsDTO.getEmail() + "\n" +
                    (contactUsDTO.getPhoneNumber() != null && !contactUsDTO.getPhoneNumber().isEmpty() ? "Phone: " + contactUsDTO.getPhoneNumber() + "\n" : "") +
                    "Subject: " + contactUsDTO.getSubject() + "\n\n" +
                    "Message:\n" + contactUsDTO.getMessage();

            emailService.sendEmail(from, to, subject, body);
            logger.info("Email sent to {} from {} with subject: {}", to, from, subject);

            ContactUsSubmission submission = new ContactUsSubmission();
            submission.setName(contactUsDTO.getName());
            submission.setEmail(contactUsDTO.getEmail());
            submission.setMessage(contactUsDTO.getMessage());
            submission.setPhoneNumber(contactUsDTO.getPhoneNumber());
            submission.setSubject(contactUsDTO.getSubject());
            contactUsSubmissionRepository.save(submission);
            logger.info("Contact us submission saved for email: {}", contactUsDTO.getEmail());

        } catch (Exception e) {
            logger.error("Failed to process contact us form for email: {}. Error: {}", contactUsDTO.getEmail(), e.getMessage());
            throw new RuntimeException("Failed to process your request. Please try again later.", e);
        }
    }
}