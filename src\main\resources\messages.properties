msg.course_created_success=course created successfully
msg.course_get_success=course get successfully
msg.course_update_success=course updated successfully
msg.course_update_failed=course updated failed
msg.course_deleted_success=course deleted successfully
msg.course_deleted_warning=action failed . Only course which has draft status can be deleted
msg.course_publish_success=course published successfully
msg.course_draft_success=course changed to draft successfully
msg.topic_move_success=topic moved successfully
msg.quiz_created_success=quiz created successfully
msg.quiz_get_success=quiz get successfully
msg.quiz_update_success=quiz updated successfully
msg.quiz_update_failed=quiz updated failed
msg.quiz_deleted_success=quiz deleted successfully
msg.quiz_publish_success=quiz published successfully
msg.quiz_draft_success=quiz changed to draft successfully
msg.question_created_success=question created successfully
msg.question_get_success=question get successfully
msg.question_update_success=question updated successfully
msg.question_deleted_success=question deleted successfully
msg.question_update_failed=question updated failed
msg.addtocart_get_success=addtocart get successfull
msg.addtocart_added_success=addtocart Added successFully
msg.addtocart_deleted_success=addtocart Removed successFully
msg.answer_saved_success=answer saved successfully
msg.answer_updated_success=answer updated successfully
msg.login_success=login successful

msg.role_mismatch=role mismatch
msg.user_inactive=user is inactive for long time or blocked by administrator please contact us
msg.user_created_success=user created  successful
msg.user_social_created_failed=user registration failed

msg.userdetails_get_success=User Details get successfully

msg.login_failed=User Email or password is wrong
msg.user_not_found=User not found




msg.resource_added_success=Resource added successfully
msg.resource_get_success=Resource get successfully
msg.resource_deleted_success=Resource deleted successfully
msg.thumbnail_added_success=Thumbnail added successfully
#marketing
msg.email_sent_success=email sent successfully
msg.bulk_email_sent_success=Bulk email sent successfully to all selected applicants
msg.SMS_sent_success=SMS send successfully
msg.emailtemplate_get_success=email template get successfully
msg.keywords_required=At least one keyword is required
msg.invalid_keywords_format=Keywords must contain only letters, numbers, and spaces
msg.emailtemplate_added_success=email template added successfully
msg.emailtemplate_updated_success=email template updated successfully
msg.emailtemplate_deleted_success=email template deleted successfully
msg.SMStemplate_get_success=SMS template get successfully
msg.SMStemplate_added_success=SMS template added successfully
msg.SMStemplate_updated_success=SMS template updated successfully
msg.SMStemplate_deleted_success=SMS template deleted successfully
msg.blog_added_success=Blog added successfully
msg.blog_updated_success=Blog updated successfully
msg.blog_deleted_success=Blog deleted successfully
msg.blog_get_success=Blog get successfully
msg.blog_details_get_success=Blog details get successfully
#event
msg.event_added_success=Event added successfully
msg.event_updated_success=Event updated successfully
msg.event_deleted_success=Event deleted successfully
msg.event_get_success=Event get successfully
msg.eventdetails_get_success=Event details get successfully
#seopages
msg.seopages_added_success=SeoPages added successfully
msg.seopages_updated_success=SeoPages updated successfully
msg.seopages_deleted_success=SeoPages deleted successfully
msg.seopages_get_success=SeoPages get successfully
msg.seopagesdetails_get_success=SeoPages details get successfully

msg.otp_sent_success=OTP sent successfully
msg.otp_verify_success=OTP verified successfully
msg.otp_verify_failed=OTP Verification Failed
msg.otp_resend_success=OTP Re-sent successfully
msg.something_went_wrong=Something went wrong

# Company Profile Messages
msg.company_profile_created=Company profile added successfully
msg.company_profile_updated=Company profile updated successfully
msg.company_profile_fetched=Company profile details fetched successfully
msg.company_profiles_active_fetched=Non-archived and active companies fetched successfully
msg.company_profiles_archived_fetched=Archived companies fetched successfully
msg.company_profile_archived=Company archived successfully
msg.company_profile_restored=Company restored successfully
msg.company_profile_deleted=Company deleted successfully
msg.company_profile_activated=Company activated successfully
msg.company_profile_deactivated=Company deactivated successfully
msg.no_company_profile=No company profile found

# Request
msg.request_failed=Request failed

#dashboard
msg.dashboard_details_get_success=DashBoard Details get successfully

#ticket
msg.ticket_raised_success=Ticket Raised successfully
msg.feedback_submitted_success=Feedback submitted successfully
msg.feedback_get_success=Feedback get successfully

# notification
msg.notification_get_all_success=get all notification successfully
msg.notification_added_success=Notification added successfully

# notes
msg.dairynotes_details_added_success=DairyNotes Details added successfully

# profileDetails
msg.profile_details_get_success=Profile Details get successfully
msg.profile_details_added_success=Profile Details added successfully
msg.profile_details_updated_success=Profile Details updated successfully

# rating review
msg.rating_review_get_success=Rating and Review get successfully
msg.rating_review_delete_success=Rating and Review delete successfully
msg.rating_review_save_success=Rating and Review save successfully

# password
msg.password_mismatch=Password does not match
msg.password_change_success=password changed successfully
msg.password_add_success=password added successfully
msg.userdetails_changed_success=User Details changed successfully

# job posts
msg.job_created=Job created successfully
msg.jobs_fetched=Jobs fetched successfully
msg.job_fetched=Job fetched successfully
msg.job_updated=Job updated successfully
msg.job_deleted=Job deleted successfully
msg.job_status_updated=Job status updated successfully
msg.job_title_required=Job title is required
msg.job_title_id_required=jobTitleId is required
msg.valid_contact_email=Valid contact email is required
msg.min_salary_invalid=Min salary cannot be greater than max salary
msg.company_profile_required=Company profile ID is required
msg.user_required=User ID is required
msg.job_not_found=Job not found
msg.company_not_found=Company not found
msg.invalid_job_subcategory=Invalid job subcategory
msg.invalid_job_subcategory_name=Invalid job subcategory name
msg.subcategory_not_match_category=The selected subcategory does not belong to the selected category
msg.category_id_required=categoryId is required
msg.subcategory_id_required=subcategoryId is required
msg.city_id_required=cityId is required

# candidate profile
msg.candidate_profile_fetched=Candidate profile fetched successfully
msg.candidates_fetched=Candidates fetched successfully
msg.candidate_profile_created=Candidate profile created successfully
msg.candidate_profile_updated=Candidate profile updated successfully
msg.candidate_activated=Candidate activated successfully
msg.candidate_deactivated=Candidate deactivated successfully

msg.registered_user_not_found=Registered User Not found
msg.registered_user_no_linked_company=Company Not Linked To Registered User

# master data
msg.master_data_fetched=Master data fetched successfully
msg.master_data_not_found=No master data found for componentTypeIds: {0}
msg.mapping_error=Error mapping data
msg.invalid_component_type_ids=Component type IDs must not be null, empty, or contain invalid values
msg.subsubcategories_fetched=Job sub-subcategories fetched successfully.
msg.districts_fetched=Districts fetched successfully.
msg.job_skills_fetched=Job skills fetched successfully.

# resume
msg.resume.created=Resume created successfully
msg.resumes.fetched=Resumes fetched successfully
msg.resume.updated=Resume updated successfully
msg.resume.deleted=Resume deleted successfully
msg.resume.title_exists=Resume with this title already exists
msg.resume.not_found=Resume not found
msg.education.added=Education entry added successfully
msg.work_experience.added=Work experience added successfully
msg.award.added=Award added successfully

# Education
msg.education.updated=Education entry updated successfully
msg.education.deleted=Education entry deleted successfully
msg.education.not_found=Education entry not found

# Work Experience
msg.work_experience.updated=Work experience updated successfully
msg.work_experience.deleted=Work experience deleted successfully
msg.work_experience.not_found=Work experience not found

# Awards
msg.award.updated=Award updated successfully
msg.award.deleted=Award deleted successfully
msg.award.not_found=Award not found

# Validation
msg.validation.end_date_before_start=End date cannot be before start date
msg.validation.future_date=Date cannot be in the future


# Validation
msg.validation.title_required=Resume title is required
msg.validation.institution_required=Institution name is required
msg.validation.degree_required=Degree name is required
msg.validation.start_date_required=Start date is required
msg.validation.job_title_required=Job title is required
msg.validation.company_required=Company name is required
msg.validation.work_start_date_required=Work start date is required
msg.validation.award_title_required=Award title is required
msg.validation.issuer_required=Award issuer is required
msg.validation.issue_date_required=Issue date is required
msg.validation.failed=Validation failed

# Errors
msg.generic_error=An error occurred

# retry
msg.retry=Something went wrong while processing your request. Would you like to retry?
msg.delete_token_generated=Delete confirmation token generated

# job application
msg.job_application_submit_success=Job application submitted successfully.
msg.job_application_count_fetch_success=Job application count retrieved successfully.
msg.job_application_status_update_success=Job application status updated successfully.
msg.job_application_retrieve_success=Job applications retrieved successfully.
msg.job_application_has_applied_check_success=Job application status check successful.
msg.already_applied=You have already applied for this job.
msg.candidate_profile_not_found=Candidate profile not found.
msg.job_application_not_found=Job application not found.
msg.company_profile_not_found=Company profile not found.
msg.invalid_role=Invalid user role.
msg.no_permission=You do not have permission to access this resource
msg.applicant_ids_required=Applicant IDs are required
msg.email_subject_required=Email subject is required
msg.email_body_required=Email body is required
msg.subcategories_fetched=Job subcategories retrieved successfully.
msg.resource_subcategories_fetched=Resource subcategories retrieved successfully.

# SEO and Programmatic Pages
msg.categories_fetched=Categories fetched successfully.
msg.seo_subcategories_fetched=SEO subcategories fetched successfully.
msg.seo_subsubcategories_fetched=SEO sub-subcategories fetched successfully.
msg.programmatic_categories_fetched=Programmatic categories fetched successfully.
msg.programmatic_subcategories_fetched=Programmatic subcategories fetched successfully.
msg.programmatic_page_created=Programmatic page created successfully.
msg.programmatic_page_updated=Programmatic page updated successfully.
msg.programmatic_page_deleted=Programmatic page deleted successfully.
msg.programmatic_pages_fetched=Programmatic pages fetched successfully.
msg.programmatic_page_fetched=Programmatic page fetched successfully.
msg.city_state_combinations_fetched=Locations fetched successfully.
msg.seo_metadata_generated=SEO metadata generated successfully
msg.bulk_seo_metadata_generated=Bulk SEO metadata generated successfully
msg.error_generating_seo_metadata=Error generating SEO metadata
msg.error_generating_bulk_seo_metadata=Error generating bulk SEO metadata


# contactUs
msg.contact_us_form_success=Thank you for reaching out! Your message has been sent successfully.
msg.contact_us_form_error=Something went wrong. Please try again later.
msg.contact_us_name_mandatory=Name is mandatory.
msg.contact_us_email_mandatory=Email is mandatory.
msg.contact_us_email_valid=Email should be valid.
msg.contact_us_message_mandatory=Message is mandatory.

# s3 bucket
msg.file_cannot_be_empty=File cannot be empty
msg.file_uploaded_success=File uploaded successfully
msg.file_processing_error=File processing error
msg.file_storage_error=File storage error
msg.file_deleted=File deleted successfully
error.file_deletion_failed=Failed to delete file from storage

# auth login
msg.email_registered_different_role=Email is already registered under a different role.
msg.email_registered_as_admin=This Email ID is already registered as an Admin; please use the correct login.
msg.email_registered_as_manager=This Email ID is already registered as a Manager; please use the correct login.
msg.email_registered_as_staff=This Email ID is already registered as Staff; please use the correct login.
msg.email_registered_as_recruiter=This Email ID is already registered as a Recruiter; please use the correct login.
msg.email_registered_as_candidate=This Email ID is already registered as a Candidate; please use the correct login.
msg.email_registered_as_user=This Email ID is already registered as a User; please use the correct login.
msg.email_registered_under_different_role=This Email ID is already registered under a different role; please use the correct login.
msg.success=Operation completed successfully
msg.mobile_number_required=Mobile number is required

msg.invalid_specialisms=Invalid specialisms provided.


msg.checkout.session.created=Checkout session created successfully
msg.subscription.details.fetched=Subscription details retrieved successfully
msg.subscription.cancelled=Subscription cancelled successfully

msg.salary_currency_required=Salary currency is required
msg.pay_type_required=Pay type is required
msg.invalid_salary_currency=Invalid salary currency
msg.invalid_pay_type=Invalid pay type
msg.min_salary_required=Minimum salary is required
msg.max_salary_required=Maximum salary is required

msg.invalid_deadline_range=Application deadline must be between job opening date and 30 days after

msg.responsibilities_and_benefits_required=Responsibilities and benefits are required

# job applicants
msg.job_applicants_retrieve_success=Job applicants retrieved successfully.
msg.company_profile_incomplete=Please complete your company profile to view applicants.
msg.job_not_owned=You do not have permission to access this job.
msg.invalid_status=Invalid application status ID provided.
msg.invalid_job_category_name=Invalid job category name
msg.jobs_with_applicants_retrieve_success=Jobs with applicants retrieved successfully.
msg.no_search_parameters=At least one search parameter is required for programmatic page search.
msg.no_programmatic_pages_found=No programmatic pages found.

msg.job_post_limit_reached=You have reached your job post limit for this month. Please upgrade your subscription or wait until the next month.


