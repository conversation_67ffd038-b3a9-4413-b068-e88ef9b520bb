package com.job.jobportal.controller;

import com.job.jobportal.dto.JobCreationPageResponse;
import com.job.jobportal.dto.JobCreationResponse;
import com.job.jobportal.dto.JobDTO;
import com.job.jobportal.model.JobStatus;
import com.job.jobportal.response.ApiResponse;
import com.job.jobportal.service.JobService;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/jobs")
public class JobController {

    @Autowired
    private JobService jobService;

    @Autowired
    private MessageSource messageSource;

    private static final Logger logger = LoggerFactory.getLogger(JobController.class);

    @PostMapping
    public ResponseEntity<?> createJob(@RequestBody JobDTO jobDTO, HttpServletRequest request) {
        try {
            JobCreationResponse createdJob=jobService.createJob(jobDTO,request);
            String message = messageSource.getMessage("msg.job_created", null, LocaleContextHolder.getLocale());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.CREATED, true, createdJob, message), HttpStatus.CREATED);
        } catch (RuntimeException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);
        }
    }

    @GetMapping
    public ResponseEntity<?> getAllJobs(
            @RequestParam(required = false) JobStatus status,
            @RequestParam(required = false) String location,
            @RequestParam(required = false) Long salary,
            @RequestParam(required = false) String jobType,
            @RequestParam(required = false) String careerLevel,
            @RequestParam(required = false) String gender,
            @RequestParam(required = false) String industry,
            @RequestParam(required = false) String qualification,
            @RequestParam(required = false) String searchTerm,
            @RequestParam(required = false) String jobTitle,
            @RequestParam(required = false) String specialisms,
            @RequestParam(required = false) String experience,
            @RequestParam(required = false) String country,
            @RequestParam(required = false) String city,
            @RequestParam(required = false) Integer category,
            @RequestParam(required = false) Integer subCategory,
            @RequestParam(required = false) Integer subSubCategory,
            @RequestParam(required = false) String categoryName,
            @RequestParam(required = false) String subCategoryName,
            @RequestParam(required = false) String subSubCategoryName,
            @RequestParam(defaultValue = "postedDate") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            if (jobTitle == null && searchTerm != null && !searchTerm.isEmpty()) {
                jobTitle = searchTerm;
            }

            JobCreationPageResponse jobs = jobService.getAllJobs(status, location, salary, jobType,
                    careerLevel, gender, industry, qualification, jobTitle, specialisms, experience, country, city,
                    category, subCategory, subSubCategory, categoryName, subCategoryName, subSubCategoryName,
                    sortBy, sortDir, page, size);
            String message = messageSource.getMessage("msg.jobs_fetched", null, LocaleContextHolder.getLocale());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, jobs, message), HttpStatus.OK);
        } catch (RuntimeException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/company")
    public ResponseEntity<?> getCompanyJobs(
            @RequestParam(required = false) JobStatus status,
            @RequestParam(required = false) String location,
            @RequestParam(required = false) Long salary,
            @RequestParam(required = false) String jobType,
            @RequestParam(required = false) String careerLevel,
            @RequestParam(required = false) String gender,
            @RequestParam(required = false) String industry,
            @RequestParam(required = false) String qualification,
            @RequestParam(required = false) String searchTerm,
            @RequestParam(required = false) String jobTitle,
            @RequestParam(required = false) String specialisms,
            @RequestParam(required = false) String experience,
            @RequestParam(required = false) String country,
            @RequestParam(required = false) String city,
            @RequestParam(required = false) Integer category,
            @RequestParam(required = false) Integer subCategory,
            @RequestParam(required = false) Integer subSubCategory,
            @RequestParam(required = false) String categoryName,
            @RequestParam(required = false) String subCategoryName,
            @RequestParam(required = false) String subSubCategoryName,
            @RequestParam(defaultValue = "postedDate") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        try {
            if (jobTitle == null && searchTerm != null && !searchTerm.isEmpty()) {
                jobTitle = searchTerm;
            }

            JobCreationPageResponse jobs = jobService.getCompanyJobs(
                    status, location, salary, jobType, careerLevel, gender, industry,
                    qualification, jobTitle, specialisms, experience, country, city,
                    category, subCategory, subSubCategory, categoryName, subCategoryName, subSubCategoryName,
                    sortBy, sortDir, page, size
            );
            String message = messageSource.getMessage("msg.jobs_fetched", null, LocaleContextHolder.getLocale());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, jobs, message), HttpStatus.OK);
        } catch (RuntimeException e) {
            logger.error("Error in getCompanyJobs: {}", e.getMessage(), e);

            if (e.getMessage() != null && e.getMessage().contains("Authentication error")) {
                return new ResponseEntity<>(new ApiResponse<>(HttpStatus.UNAUTHORIZED, false, null, "You must be logged in to access company jobs"), HttpStatus.UNAUTHORIZED);
            }

            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/{jobId}")
    public ResponseEntity<?> getJobById(@PathVariable Long jobId) {
        try {
            JobCreationResponse job = jobService.getJobById(jobId);
            String message = messageSource.getMessage("msg.job_fetched", null, LocaleContextHolder.getLocale());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, job, message), HttpStatus.OK);
        } catch (RuntimeException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.NOT_FOUND, false, e.getMessage()), HttpStatus.NOT_FOUND);
        }
    }

    @PutMapping("/{jobId}")
    public ResponseEntity<?> updateJob(@PathVariable Long jobId, @RequestBody JobDTO jobDTO) {
        try {
            JobDTO updatedJob = jobService.updateJob(jobId, jobDTO);
            String message = messageSource.getMessage("msg.job_updated", null, LocaleContextHolder.getLocale());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, updatedJob, message), HttpStatus.OK);
        } catch (RuntimeException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);
        }
    }

    @PutMapping("/{jobId}/status")
    public ResponseEntity<?> updateJobStatus(@PathVariable Long jobId, @RequestParam String status) {
        try {
            JobDTO updatedJob = jobService.updateJobStatus(jobId, status);
            String message = messageSource.getMessage("msg.job_status_updated", null, LocaleContextHolder.getLocale());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, updatedJob, message), HttpStatus.OK);
        } catch (RuntimeException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);
        }
    }


    @GetMapping("/company/{companyProfileId}/active-count")
    public ResponseEntity<?> getActiveJobCountForCompany(@PathVariable Long companyProfileId) {
        try {
            long count = jobService.getActiveJobCountForCompany(companyProfileId);
            logger.info("Active job count retrieved for companyProfileId: {}", companyProfileId);
            String message = messageSource.getMessage("msg.jobs_fetched", null, LocaleContextHolder.getLocale());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, count, message), HttpStatus.OK);
        } catch (RuntimeException e) {
            logger.error("Error retrieving active job count: {}", e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, null, e.getMessage()), HttpStatus.BAD_REQUEST);
        }
    }

    @GetMapping("/company/active-count")
    public ResponseEntity<?> getActiveJobCountForCurrentCompany() {
        try {
            long count = jobService.getActiveJobCountForCurrentCompany();
            logger.info("Active job count retrieved for current company");
            String message = messageSource.getMessage("msg.jobs_fetched", null, LocaleContextHolder.getLocale());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, count, message), HttpStatus.OK);
        } catch (RuntimeException e) {
            logger.error("Error retrieving active job count: {}", e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, null, e.getMessage()), HttpStatus.BAD_REQUEST);
        }
    }

    @DeleteMapping("/{jobId}")
    public ResponseEntity<?> deleteJob(@PathVariable Long jobId) {
        try {
            jobService.deleteJob(jobId);
            String message = messageSource.getMessage("msg.job_deleted", null, LocaleContextHolder.getLocale());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.NO_CONTENT, true, null, message), HttpStatus.NO_CONTENT);
        } catch (RuntimeException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.NOT_FOUND, false, e.getMessage()), HttpStatus.NOT_FOUND);
        }
    }
}